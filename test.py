import os
import shutil
import time
from pathlib import Path
from src.document_processing.training_pipeline import TrainingPipeline
import json

RAW_DIR = Path("data/raw_files")
CACHE_FILE = Path("data/cache/processed_chunks.json")
TEST_FILE = RAW_DIR / "dedup_test.txt"


def print_cache():
    if CACHE_FILE.exists():
        with open(CACHE_FILE, 'r', encoding='utf-8') as f:
            cache = json.load(f)
        print(f"Cache now has {len(cache)} entries.")
    else:
        print("No cache file found.")

def run_pipeline():
    pipeline = TrainingPipeline()
    pipeline.process_hr_files()

def main():
    RAW_DIR.mkdir(parents=True, exist_ok=True)
    # Step 1: Create a test file
    with open(TEST_FILE, 'w', encoding='utf-8') as f:
        f.write("This is a test sentence.\nThis is another test sentence.")
    print("\n--- First run: Should process all chunks ---")
    run_pipeline()
    print_cache()
    time.sleep(1)

    # Step 2: Run again without changes
    print("\n--- Second run: Should skip all chunks as duplicates ---")
    run_pipeline()
    print_cache()
    time.sleep(1)

    # Step 3: Modify the file (add a new line)
    with open(TEST_FILE, 'a', encoding='utf-8') as f:
        f.write("\nThis is a new, unique sentence.")
    print("\n--- Third run: Should process only new chunk(s) ---")
    run_pipeline()
    print_cache()

    # Cleanup (optional)
    # TEST_FILE.unlink()

if __name__ == "__main__":
    main() 