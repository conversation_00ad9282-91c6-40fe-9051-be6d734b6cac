<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message Input Layout Test</title>
    <link rel="stylesheet" href="static/css/chatgpt-style-clean.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f9fafb;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .test-container {
            width: 100%;
            max-width: 800px;
            padding: 20px;
        }

        .preview-text {
            text-align: center;
            margin-bottom: 40px;
            color: #374151;
            font-size: 18px;
        }

        /* Test functionality */
        .functionality-test {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
        }

        .test-pass {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .test-fail {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="preview-text">
            <h2>Message Input Layout Preview</h2>
            <p>This is a test of the new message input layout matching the provided image.</p>
        </div>
        
        <footer class="chat-input-container">
            <form id="chatForm" class="chat-input-form">
                <!-- Single row layout: left actions, textarea, right actions -->
                <div class="input-horizontal-row">
                    <div class="left-actions">
                        <button type="button" id="quickUploadBtn" class="action-btn" title="Upload Documents">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <button type="button" id="voiceInputBtn" class="action-btn" title="Voice Input">
                            <i class="fas fa-microphone"></i>
                        </button>
                    </div>

                    <!-- Main input area -->
                    <div class="input-main-area">
                        <textarea id="userInput" placeholder="How can I help you today?" rows="1" autocomplete="off"></textarea>
                    </div>

                    <div class="right-actions">
                        <button type="submit" id="sendBtn" class="send-btn" title="Send message">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </form>
        </footer>

        <div class="functionality-test">
            <h3>Layout Verification</h3>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        // Test functionality
        document.addEventListener('DOMContentLoaded', function() {
            const results = document.getElementById('test-results');
            const tests = [];

            // Test 1: Check if input container exists and has correct styling
            const container = document.querySelector('.input-horizontal-row');
            tests.push({
                name: 'Input container exists',
                pass: container !== null
            });

            // Test 2: Check if left actions are present
            const leftActions = document.querySelector('.left-actions');
            tests.push({
                name: 'Left actions (paperclip, microphone) present',
                pass: leftActions && leftActions.children.length >= 2
            });

            // Test 3: Check if textarea is present
            const textarea = document.querySelector('#userInput');
            tests.push({
                name: 'Textarea input field present',
                pass: textarea !== null
            });

            // Test 4: Check if send button is present
            const sendBtn = document.querySelector('.send-btn');
            tests.push({
                name: 'Send button present',
                pass: sendBtn !== null
            });

            // Test 5: Check if layout has proper styling
            if (container) {
                const styles = window.getComputedStyle(container);
                tests.push({
                    name: 'Container has rounded border',
                    pass: styles.borderRadius === '24px'
                });

                tests.push({
                    name: 'Container has proper height',
                    pass: styles.height === '48px'
                });
            }

            // Display results
            tests.forEach(test => {
                const div = document.createElement('div');
                div.className = `test-result ${test.pass ? 'test-pass' : 'test-fail'}`;
                div.textContent = `${test.pass ? '✓' : '✗'} ${test.name}`;
                results.appendChild(div);
            });

            // Test input functionality
            if (textarea) {
                textarea.addEventListener('input', function() {
                    console.log('Input detected:', this.value);
                });
            }

            // Test button clicks
            document.querySelectorAll('.action-btn, .send-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Button clicked:', this.title || this.className);
                });
            });
        });
    </script>
</body>
</html>
