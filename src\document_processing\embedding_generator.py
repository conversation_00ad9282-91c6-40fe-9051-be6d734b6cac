"""
Embedding generation and direct push to Qdrant Cloud with Singleton optimization.
"""

import os
import numpy as np
from typing import List, Dict, Any, Optional
from pathlib import Path
from uuid import uuid4
from dotenv import load_dotenv

import torch
import torch.nn.functional as F
from sentence_transformers import Sen<PERSON>ceTransformer
from transformers import <PERSON>Tokenizer, AutoModel

from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct

from ..utils.logger import get_logger
from ..config import EMBEDDING_MODEL_NAME, VECTOR_DIMENSION

# Load environment variables
load_dotenv()
logger = get_logger(__name__)

QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")
QDRANT_URL = os.getenv("QDRANT_URL")
QDRANT_COLLECTION_NAME = os.getenv("QDRANT_COLLECTION_NAME")

class EmbeddingGenerator:
    """Handles embedding generation and syncing with Qdrant Cloud (Singleton-enabled)."""

    _instance = None

    def __new__(cls, model_name: str = EMBEDDING_MODEL_NAME, force_cpu: bool = False):
        if cls._instance is None:
            cls._instance = super(EmbeddingGenerator, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, model_name: str = EMBEDDING_MODEL_NAME, force_cpu: bool = False):
        if self._initialized:
            return
        self.model_name = model_name
        self.force_cpu = force_cpu
        self.device = self._select_device()
        self._initialize_model()
        self._detect_embedding_dimension()

        self.qdrant_client = QdrantClient(
            url=QDRANT_URL,
            api_key=QDRANT_API_KEY
        )
        self._initialized = True

    def _select_device(self):
        if self.force_cpu:
            logger.info("🔧 Forcing CPU-only mode")
            return torch.device("cpu")
        if torch.cuda.is_available():
            logger.info("🚀 Using GPU acceleration")
            return torch.device("cuda")
        if hasattr(torch.backends, "mps") and torch.backends.mps.is_available():
            logger.info("🍎 Using Apple Silicon GPU")
            return torch.device("mps")
        logger.info("💻 Using CPU")
        return torch.device("cpu")

    def _initialize_model(self):
        try:
            self.model = SentenceTransformer(self.model_name, device=str(self.device))
            self.model_type = "sentence_transformer"
            self.tokenizer = None
            logger.info(f"✅ Loaded SentenceTransformer model: {self.model_name}")
        except Exception as e:
            logger.warning(f"❌ SentenceTransformer failed: {e}. Falling back to HuggingFace.")
            self.model = AutoModel.from_pretrained(self.model_name).to(self.device)
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model_type = "huggingface"
            self.model.eval()
            logger.info(f"✅ Loaded HuggingFace model: {self.model_name}")

    def _detect_embedding_dimension(self):
        try:
            test_vector = self._generate_embedding("test")
            self.actual_dimension = len(test_vector)
            if self.actual_dimension != VECTOR_DIMENSION:
                logger.warning(f"⚠️ Embedding dimension mismatch: config={VECTOR_DIMENSION}, actual={self.actual_dimension}")
        except Exception as e:
            logger.error(f"❌ Dimension detection failed: {e}")
            self.actual_dimension = VECTOR_DIMENSION

    def _generate_embedding(self, text: str) -> np.ndarray:
        return self.generate_embeddings([text])[0]

    def _mean_pooling(self, model_output, attention_mask):
        token_embeddings = model_output[0]
        input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
        pooled = torch.sum(token_embeddings * input_mask_expanded, dim=1)
        return pooled / torch.clamp(input_mask_expanded.sum(1), min=1e-9)

    def _huggingface_encode(self, texts: List[str]) -> np.ndarray:
        encoded_input = self.tokenizer(
            texts, padding=True, truncation=True, return_tensors='pt', max_length=512
        ).to(self.device)

        with torch.no_grad():
            model_output = self.model(**encoded_input)

        pooled = self._mean_pooling(model_output, encoded_input["attention_mask"])
        return F.normalize(pooled, p=2, dim=1).cpu().numpy()

    def generate_embeddings(self, texts: List[str]) -> np.ndarray:
        if self.model_type == "sentence_transformer":
            return self.model.encode(texts, convert_to_numpy=True, batch_size=32)
        return self._huggingface_encode(texts)

    def generate(self, text: str) -> np.ndarray:
        if not text.strip():
            return np.zeros(self.actual_dimension)
        return self._generate_embedding(text)

    def generate_and_store_embeddings(self, documents: List[Dict[str, Any]]) -> None:
        if not documents:
            logger.warning("⚠️ No documents provided for embedding")
            return

        texts = [doc["content"] for doc in documents if doc.get("content")]
        payloads = [doc for doc in documents if doc.get("content")]

        if not texts:
            logger.warning("⚠️ No valid document texts found")
            return

        logger.info(f"🧠 Generating embeddings for {len(texts)} documents")
        embeddings = self.generate_embeddings(texts)

        points = [
            PointStruct(id=str(uuid4()), vector=vec.tolist(), payload=payload)
            for vec, payload in zip(embeddings, payloads)
        ]

        logger.info(f"📡 Pushing {len(points)} vectors to Qdrant Cloud")
        self.qdrant_client.upsert(
            collection_name=QDRANT_COLLECTION_NAME,
            points=points
        )

    def generate_query_embedding(self, query: str) -> np.ndarray:
        return self._generate_embedding(query)

    def get_model_info(self) -> Dict[str, Any]:
        return {
            "model_name": self.model_name,
            "model_type": self.model_type,
            "device": str(self.device),
            "embedding_dimension": self.actual_dimension,
            "config_dimension": VECTOR_DIMENSION,
            "dimension_match": self.actual_dimension == VECTOR_DIMENSION,
        }
