"""
Process different file types for document ingestion.
"""
import os
import re
import unicodedata
from typing import Dict, Any, Callable
from pathlib import Path
import logging

# Optional imports
try:
    import docx
except ImportError:
    docx = None

try:
    import fitz  # PyMuPDF
except ImportError:
    fitz = None

try:
    import pandas as pd
except ImportError:
    pd = None

try:
    from pptx import Presentation
except ImportError:
    Presentation = None

try:
    import textract
except ImportError:
    textract = None

try:
    import extract_msg
except ImportError:
    extract_msg = None

try:
    import markdown
    from bs4 import BeautifulSoup
    markdown_available = True
except ImportError:
    markdown_available = False
    # Still need BeautifulSoup for HTML processing
    try:
        from bs4 import BeautifulSoup
    except ImportError:
        BeautifulSoup = None

try:
    from PIL import Image
    import pytesseract
    ocr_available = True
except ImportError:
    Image = None
    pytesseract = None
    ocr_available = False

logger = logging.getLogger(__name__)

class FileProcessor:
    """Process different file types for text extraction."""

    MAX_FILE_SIZE_MB = 20

    @staticmethod
    def _get_handler(extension: str) -> Callable:
        return {
            '.pdf': FileProcessor.extract_from_pdf,
            '.docx': FileProcessor.extract_from_docx,
            '.doc': FileProcessor.extract_from_doc,
            '.txt': FileProcessor.extract_from_txt,
            '.md': FileProcessor.extract_from_markdown,
            '.csv': FileProcessor.extract_from_csv,
            '.xls': FileProcessor.extract_from_excel,
            '.xlsx': FileProcessor.extract_from_excel,
            '.pptx': FileProcessor.extract_from_pptx,
            '.html': FileProcessor.extract_from_html,
            '.htm': FileProcessor.extract_from_html,
            '.msg': FileProcessor.extract_from_msg,
            '.jpg': FileProcessor.extract_from_image,
            '.jpeg': FileProcessor.extract_from_image,
            '.png': FileProcessor.extract_from_image,
        }.get(extension, FileProcessor.extract_from_txt)

    @staticmethod
    def _available_extensions() -> set:
        """Get available extensions based on installed dependencies."""
        exts = set()
        if fitz: 
            exts.add('.pdf')
        if docx: 
            exts.add('.docx')
        if textract: 
            exts.add('.doc')
        if pd: 
            exts.update({'.csv', '.xls', '.xlsx'})
        if Presentation: 
            exts.add('.pptx')
        if markdown_available: 
            exts.add('.md')
        
        # These don't require special dependencies
        exts.update({'.txt'})
        
        # HTML requires BeautifulSoup
        if BeautifulSoup:
            exts.update({'.html', '.htm'})
            
        if extract_msg: 
            exts.add('.msg')
        if ocr_available: 
            exts.update({'.jpg', '.jpeg', '.png'})
        return exts

    # Fix: Call the static method properly
    SUPPORTED_EXTENSIONS = _available_extensions()

    @staticmethod
    def process_file(file_path: Path) -> Dict[str, Any]:
        """Process a file and extract its text content."""
        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return FileProcessor._error_doc(file_path, "FILE NOT FOUND")

        if file_path.stat().st_size > FileProcessor.MAX_FILE_SIZE_MB * 1024 * 1024:
            logger.warning(f"File too large: {file_path.name}")
            return FileProcessor._error_doc(file_path, "FILE TOO LARGE")

        ext = file_path.suffix.lower()
        title = FileProcessor._generate_title(file_path.name, ext)
        
        # Check if extension is supported
        if ext not in FileProcessor.SUPPORTED_EXTENSIONS:
            logger.warning(f"Unsupported file type: {ext} for file {file_path.name}")
            return FileProcessor._error_doc(file_path, f"UNSUPPORTED FILE TYPE: {ext}", title)
        
        handler = FileProcessor._get_handler(ext)

        try:
            content = handler(file_path)
            content = unicodedata.normalize('NFKC', content or "").strip()
            if not content:
                content = f"[EMPTY CONTENT: {file_path.name}]"
            elif len(content) < 50:
                logger.warning(f"Very short content extracted from {file_path} ({len(content)} characters)")

            logger.info(f"Processed {ext.upper()} file: {file_path.name} ({len(content)} characters)")
            return {
                "title": title,
                "content": content,
                "source_file": str(file_path),
                "file_type": ext.lstrip('.')
            }

        except (IOError, UnicodeDecodeError, ValueError) as e:
            logger.error(f"Error processing file {file_path}: {e}")
            return FileProcessor._error_doc(file_path, str(e), title)

        except Exception as e:
            logger.exception(f"Unexpected error processing file {file_path}")
            return FileProcessor._error_doc(file_path, f"UNEXPECTED: {e}", title)

    @staticmethod
    def _generate_title(file_name: str, file_extension: str) -> str:
        """Generate a clean title from filename."""
        title = file_name.replace(file_extension, '').replace('-', ' ').replace('_', ' ')
        return re.sub(r'\s+', ' ', title).strip().title()

    @staticmethod
    def _error_doc(file_path: Path, error: str, title: str = None) -> Dict[str, Any]:
        """Create an error document."""
        return {
            "title": title or f"Error: {file_path.name}",
            "content": f"[{error}: {file_path.name}]",
            "source_file": str(file_path),
            "file_type": "error"
        }

    @staticmethod
    def extract_from_pdf(file_path: Path) -> str:
        """Extract text from PDF using PyMuPDF."""
        if fitz is None:
            return "[PDF support not installed - install PyMuPDF: pip install PyMuPDF]"
        try:
            with fitz.open(file_path) as pdf:
                text = []
                for page_num, page in enumerate(pdf, 1):
                    try:
                        page_text = page.get_text()
                        if page_text.strip():  # Only add non-empty pages
                            text.append(page_text)
                    except Exception as page_err:
                        logger.warning(f"Failed to extract page {page_num} from {file_path.name}: {page_err}")
                return "\n".join(text)
        except Exception as e:
            raise IOError(f"PDF read failure: {e}")

    @staticmethod
    def extract_from_docx(file_path: Path) -> str:
        """Extract text from DOCX file."""
        if docx is None:
            return "[DOCX support not installed - install: pip install python-docx]"
        try:
            doc = docx.Document(file_path)
            paragraphs = [p.text for p in doc.paragraphs if p.text.strip()]
            return "\n".join(paragraphs)
        except Exception as e:
            raise IOError(f"DOCX read failure: {e}")

    @staticmethod
    def extract_from_doc(file_path: Path) -> str:
        """Extract text from DOC file using textract."""
        if textract is None:
            return "[DOC support not installed - install: pip install textract]"
        try:
            content = textract.process(str(file_path))
            return content.decode('utf-8', errors='replace')
        except Exception as e:
            raise ValueError(f"Textract failed: {e}")

    @staticmethod
    def extract_from_txt(file_path: Path) -> str:
        """Extract text from plain text file with encoding detection."""
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        for enc in encodings:
            try:
                with open(file_path, 'r', encoding=enc) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue
        
        # Last resort: read as binary and replace errors
        try:
            with open(file_path, 'rb') as f:
                return f.read().decode('utf-8', errors='replace')
        except Exception as e:
            raise IOError(f"Failed to read text file: {e}")

    @staticmethod
    def extract_from_markdown(file_path: Path) -> str:
        """Extract text from Markdown file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                md_text = f.read()
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='latin-1') as f:
                md_text = f.read()
        
        if not markdown_available:
            return md_text  # Return raw markdown if parser not available
        
        try:
            html = markdown.markdown(md_text)
            return BeautifulSoup(html, 'html.parser').get_text()
        except Exception:
            return md_text  # Fallback to raw markdown

    @staticmethod
    def extract_from_csv(file_path: Path) -> str:
        """Extract text from CSV file."""
        if pd is None:
            return "[Pandas not installed for CSV processing - install: pip install pandas]"
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            return df.to_string(index=False)
        except UnicodeDecodeError:
            df = pd.read_csv(file_path, encoding='latin-1')
            return df.to_string(index=False)
        except Exception as e:
            raise IOError(f"CSV read failure: {e}")

    @staticmethod
    def extract_from_excel(file_path: Path) -> str:
        """Extract text from Excel file."""
        if pd is None:
            return "[Pandas not installed for Excel processing - install: pip install pandas openpyxl]"
        try:
            # Read all sheets
            excel_file = pd.ExcelFile(file_path)
            sheets_content = []
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                if not df.empty:
                    sheets_content.append(f"=== Sheet: {sheet_name} ===")
                    sheets_content.append(df.to_string(index=False))
            return "\n\n".join(sheets_content)
        except Exception as e:
            raise IOError(f"Excel read failure: {e}")

    @staticmethod
    def extract_from_pptx(file_path: Path) -> str:
        """Extract text from PowerPoint file."""
        if Presentation is None:
            return "[PPTX support not installed - install: pip install python-pptx]"
        try:
            prs = Presentation(file_path)
            slides_content = []
            for i, slide in enumerate(prs.slides, 1):
                slide_text = []
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text.strip())
                if slide_text:
                    slides_content.append(f"=== Slide {i} ===")
                    slides_content.extend(slide_text)
            return "\n".join(slides_content)
        except Exception as e:
            raise IOError(f"PPTX read failure: {e}")

    @staticmethod
    def extract_from_html(file_path: Path) -> str:
        """Extract text from HTML file."""
        if BeautifulSoup is None:
            return "[HTML support not installed - install: pip install beautifulsoup4]"
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='latin-1') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        return soup.get_text(separator='\n', strip=True)

    @staticmethod
    def extract_from_msg(file_path: Path) -> str:
        """Extract text from Outlook MSG file."""
        if extract_msg is None:
            return "[MSG support not installed - install: pip install extract-msg]"
        try:
            msg = extract_msg.Message(str(file_path))
            content_parts = []
            if msg.subject:
                content_parts.append(f"Subject: {msg.subject}")
            if msg.body:
                content_parts.append(f"Body: {msg.body}")
            return "\n".join(content_parts)
        except Exception as e:
            raise IOError(f"MSG read failure: {e}")

    @staticmethod
    def extract_from_image(file_path: Path) -> str:
        """Extract text from image using OCR."""
        if not ocr_available:
            return "[OCR support not installed - install: pip install Pillow pytesseract]"
        try:
            image = Image.open(file_path)
            text = pytesseract.image_to_string(image)
            return text.strip()
        except Exception as e:
            raise IOError(f"OCR failed: {e}")