"""
Document version control and re-indexing for Qdrant-based vector store (production-ready).
"""
import hashlib
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
import logging

from ..utils.logger import get_logger
from ..config import DATA_DIR
from .embedding_generator import EmbeddingGenerator
from ..database.vector_store import QdrantVectorStore

logger = logging.getLogger(__name__)

VERSIONS_FILE = Path("data/cache/document_versions.json")

class DocumentVersionControl:
    """Production-grade document versioning and Qdrant re-indexing."""

    def __init__(self):
        self.versions = {}
        self._load_versions()
        self.embedding_generator = EmbeddingGenerator()
        self.vector_store = QdrantVectorStore()
        self.backup_dir = DATA_DIR / "backups"
        self.backup_dir.mkdir(exist_ok=True)

    def _load_versions(self):
        if VERSIONS_FILE.exists():
            try:
                with open(VERSIONS_FILE, 'r', encoding='utf-8') as f:
                    self.versions = json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load document_versions.json: {e}. Rebuilding from scratch.")
                self.versions = {}
        else:
            self.versions = {}

    def _save_versions(self):
        with open(VERSIONS_FILE, 'w', encoding='utf-8') as f:
            json.dump(self.versions, f, indent=2)

    def compute_hash(self, file_path):
        h = hashlib.sha256()
        with open(file_path, 'rb') as f:
            while True:
                chunk = f.read(8192)
                if not chunk:
                    break
                h.update(chunk)
        return h.hexdigest()

    def is_file_changed(self, file_path):
        file_hash = self.compute_hash(file_path)
        stored_hash = self.versions.get(str(file_path.name))
        logger.info(f"Version control check for {file_path.name}: computed_hash={file_hash}, stored_hash={stored_hash}")
        if stored_hash == file_hash:
            logger.info(f"Skipping unchanged file: {file_path.name}")
            return False
        return True

    def update_version(self, file_path):
        file_hash = self.compute_hash(file_path)
        self.versions[str(file_path.name)] = file_hash
        self._save_versions()

    def reindex_document(self, file_path: Path) -> bool:
        try:
            if not self.is_file_changed(file_path):
                logger.info(f"No re-indexing needed for {file_path.name}")
                return True

            embeddings = self.embedding_generator.generate_document_embeddings(file_path)
            docs = [
                {
                    "content": chunk["content"],
                    "source_file": str(file_path.name),
                    "chunk_index": i
                }
                for i, chunk in enumerate(embeddings)
            ]

            self.vector_store.delete_by_source_file(str(file_path.name))
            self.vector_store.add_documents(documents=docs, vectors=embeddings)
            self.update_version(file_path)
            logger.info(f"Re-indexed {file_path.name} with {len(embeddings)} chunks")
            return True
        except Exception as e:
            logger.error(f"Failed to re-index {file_path.name}: {e}")
            return False

    def backup_document(self, file_path: Path) -> Optional[Path]:
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = self.backup_dir / f"{file_path.stem}_{timestamp}{file_path.suffix}"
            shutil.copy2(file_path, backup_path)
            logger.info(f"Backup created at {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"Backup failed for {file_path.name}: {e}")
            return None

    def restore_document(self, backup_path: Path, target_path: Path) -> bool:
        try:
            shutil.copy2(backup_path, target_path)
            logger.info(f"Restored {target_path.name} from backup")
            return self.reindex_document(target_path)
        except Exception as e:
            logger.error(f"Restore failed: {e}")
            return False

    def cleanup_old_versions(self, max_versions: int = 5):
        try:
            backups = {}
            for file in self.backup_dir.glob("*"):
                key = "_".join(file.stem.split("_")[:-1])
                backups.setdefault(key, []).append(file)

            for file_group in backups.values():
                file_group.sort(key=lambda f: f.stat().st_mtime, reverse=True)
                for old_file in file_group[max_versions:]:
                    old_file.unlink()
                    logger.info(f"Old backup deleted: {old_file.name}")
        except Exception as e:
            logger.error(f"Failed to clean up backups: {e}")
