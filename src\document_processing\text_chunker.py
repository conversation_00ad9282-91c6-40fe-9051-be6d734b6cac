"""
Production-grade text chunker with token awareness, multiple strategies, and enterprise features.
"""
import re
import unicodedata
import asyncio
import time
from typing import List, Dict, Any, Optional, Generator, Tuple, Union, Callable, Literal
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor
import logging
from pathlib import Path
import hashlib
import json

# Enterprise logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Optional imports with fallbacks
try:
    from transformers import AutoTokenizer
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logger.warning("transformers not available - advanced tokenization disabled")

try:
    from langchain.text_splitter import RecursiveCharacterTextSplitter, TokenTextSplitter
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logger.warning("langchain not available - external splitters disabled")

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    logger.warning("spacy not available - advanced NLP features disabled")

try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    SEMANTIC_AVAILABLE = True
except ImportError:
    SEMANTIC_AVAILABLE = False
    logger.warning("sentence-transformers/sklearn not available - semantic chunking disabled")


@dataclass
class ChunkingMetrics:
    """Metrics for monitoring chunking performance."""
    total_chunks: int = 0
    total_tokens: int = 0
    total_characters: int = 0
    processing_time: float = 0.0
    average_chunk_size: float = 0.0
    token_efficiency: float = 0.0
    strategy_used: str = ""
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


@dataclass
class ChunkingConfig:
    """Enhanced configuration with token awareness and strategy options."""
    chunk_size: int = 800
    chunk_overlap: int = 150
    min_chunk_size: int = 100
    max_chunk_size: Optional[int] = None
    hr_mode: bool = True
    preserve_formatting: bool = True
    
    # Token-aware settings
    use_tokens: bool = True
    tokenizer_name: str = "cl100k_base"  # OpenAI GPT-4 tokenizer
    max_tokens: int = 512
    token_overlap: int = 50
    
    # Strategy settings
    strategy: Literal["auto", "token", "semantic", "recursive", "sentence", "hr"] = "auto"
    
    # Performance settings
    enable_async: bool = True
    max_workers: int = 4
    cache_tokenization: bool = True
    
    # Multilingual support
    language: str = "auto"
    spacy_model: str = "en_core_web_sm"
    
    # Quality settings
    semantic_threshold: float = 0.7
    preserve_paragraphs: bool = True
    respect_word_boundaries: bool = True

    def __post_init__(self):
        if self.chunk_overlap >= self.chunk_size:
            raise ValueError("chunk_overlap must be less than chunk_size")
        if self.min_chunk_size <= 0:
            raise ValueError("min_chunk_size must be positive")
        if self.max_chunk_size is None:
            self.max_chunk_size = self.chunk_size + self.chunk_overlap
        if self.token_overlap >= self.max_tokens:
            raise ValueError("token_overlap must be less than max_tokens")


class ChunkingError(Exception):
    """Enhanced chunking exception with context."""
    def __init__(self, message: str, context: Optional[Dict] = None):
        super().__init__(message)
        self.context = context or {}


class TokenizerManager:
    """Manages different tokenizers with caching."""
    
    def __init__(self):
        self._tokenizers = {}
        self._cache = {}
    
    def get_tokenizer(self, name: str):
        """Get or create tokenizer with caching."""
        if name not in self._tokenizers:
            if TRANSFORMERS_AVAILABLE:
                try:
                    # Always use the local cached tokenizer for sentence embedding
                    self._tokenizers[name] = AutoTokenizer.from_pretrained('data/models_cache/Sentence Embedding Model')
                except Exception as e:
                    logger.warning(f"Failed to load tokenizer from local cache: {e}")
                    # Fallback to character-based
                    self._tokenizers[name] = None
            else:
                self._tokenizers[name] = None
        return self._tokenizers[name]
    
    def count_tokens(self, text: str, tokenizer_name: str) -> int:
        """Count tokens with caching."""
        cache_key = hashlib.md5(f"{tokenizer_name}:{text}".encode()).hexdigest()
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        tokenizer = self.get_tokenizer(tokenizer_name)
        if tokenizer is None:
            # Fallback to approximate token count
            count = len(text) // 4  # Rough approximation: 1 token ≈ 4 characters
        else:
            if hasattr(tokenizer, 'encode'):
                count = len(tokenizer.encode(text))
            else:
                count = len(tokenizer.tokenize(text))
        
        self._cache[cache_key] = count
        return count
    
    def encode_with_boundaries(self, text: str, tokenizer_name: str) -> List[Tuple[int, int]]:
        """Encode text and return token boundaries."""
        tokenizer = self.get_tokenizer(tokenizer_name)
        if tokenizer is None or not hasattr(tokenizer, 'encode_with_offsets'):
            # Fallback to word boundaries
            words = text.split()
            boundaries = []
            pos = 0
            for word in words:
                start = text.find(word, pos)
                boundaries.append((start, start + len(word)))
                pos = start + len(word)
            return boundaries
        
        return tokenizer.encode_with_offsets(text)


class ChunkingStrategy(ABC):
    """Abstract base class for chunking strategies."""
    
    @abstractmethod
    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        pass


class TokenAwareStrategy(ChunkingStrategy):
    """Token-aware chunking strategy."""
    
    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        if not config.use_tokens:
            return self._fallback_char_chunking(text, config)
        
        chunks = []
        current_chunk = ""
        sentences = self._split_into_sentences(text)
        
        for sentence in sentences:
            # Test if adding this sentence would exceed token limit
            test_chunk = self._join_with_space(current_chunk, sentence)
            token_count = tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name)
            
            if token_count <= config.max_tokens:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                
                # Handle oversized sentences
                if tokenizer_manager.count_tokens(sentence, config.tokenizer_name) > config.max_tokens:
                    chunks.extend(self._split_oversized_sentence(sentence, config, tokenizer_manager))
                else:
                    current_chunk = sentence
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return self._add_token_overlap(chunks, config, tokenizer_manager)
    
    def _split_oversized_sentence(self, sentence: str, config: ChunkingConfig, 
                                   tokenizer_manager: TokenizerManager) -> List[str]:
        """Split sentences that exceed token limit while respecting word boundaries."""
        words = sentence.split()
        chunks = []
        current_chunk = ""
        
        for word in words:
            test_chunk = self._join_with_space(current_chunk, word)
            if tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name) <= config.max_tokens:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = word
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _add_token_overlap(self, chunks: List[str], config: ChunkingConfig, 
                           tokenizer_manager: TokenizerManager) -> List[str]:
        """Add overlap between chunks based on tokens."""
        if len(chunks) <= 1 or config.token_overlap <= 0:
            return chunks
        
        overlapped_chunks = [chunks[0]]
        
        for i in range(1, len(chunks)):
            prev_chunk = chunks[i-1]
            current_chunk = chunks[i]
            
            # Extract overlap from previous chunk
            overlap = self._get_token_overlap(prev_chunk, config.token_overlap, 
                                             config, tokenizer_manager)
            
            if overlap and not current_chunk.startswith(overlap):
                overlapped_chunks.append(f"{overlap} {current_chunk}")
            else:
                overlapped_chunks.append(current_chunk)
        
        return overlapped_chunks
    
    def _get_token_overlap(self, text: str, max_overlap_tokens: int, 
                           config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> str:
        """Extract overlap text based on token count."""
        sentences = self._split_into_sentences(text)
        overlap_sentences = []
        token_count = 0
        
        # Work backwards from the end
        for sentence in reversed(sentences):
            sentence_tokens = tokenizer_manager.count_tokens(sentence, config.tokenizer_name)
            if token_count + sentence_tokens <= max_overlap_tokens:
                overlap_sentences.insert(0, sentence)
                token_count += sentence_tokens
            else:
                break
        
        return " ".join(overlap_sentences)
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Enhanced sentence splitting with better abbreviation handling."""
        abbreviations = {
            'Dr', 'Mr', 'Mrs', 'Ms', 'Prof', 'Sr', 'Jr', 'Inc', 'Ltd', 'Corp',
            'Co', 'vs', 'etc', 'i.e', 'e.g', 'cf', 'al', 'St', 'Ave', 'Blvd',
            'Fig', 'Vol', 'No', 'pp', 'ch', 'sec', 'dept', 'univ', 'govt'
        }
        
        # Create pattern for abbreviations
        abbrev_pattern = r'\b(?:' + '|'.join(re.escape(a) for a in abbreviations) + r')\.'
        
        # Temporarily replace abbreviations
        temp_text = re.sub(abbrev_pattern, lambda m: m.group().replace('.', '<DOT>'), text)
        
        # Split on sentence boundaries
        sentences = re.split(r'(?<=[.!?])\s+(?=[A-Z])', temp_text)
        
        # Restore abbreviations and clean up
        return [s.replace('<DOT>', '.').strip() for s in sentences if s.strip()]
    
    def _join_with_space(self, text1: str, text2: str) -> str:
        """Join two text pieces with appropriate spacing."""
        if not text1:
            return text2
        if not text2:
            return text1
        return f"{text1} {text2}"
    
    def _fallback_char_chunking(self, text: str, config: ChunkingConfig) -> List[str]:
        """Fallback to character-based chunking."""
        chunks = []
        start = 0
        while start < len(text):
            end = start + config.chunk_size
            if end >= len(text):
                chunks.append(text[start:])
                break
            
            # Find word boundary
            while end > start and end < len(text) and not text[end].isspace():
                end -= 1
            
            if end == start:  # No space found, force break
                end = start + config.chunk_size
            
            chunks.append(text[start:end].strip())
            start = end - config.chunk_overlap
            start = max(start, 0)
        
        return [chunk for chunk in chunks if chunk]
    
    def get_name(self) -> str:
        return "token_aware"


class SemanticStrategy(ChunkingStrategy):
    """Semantic chunking based on content similarity."""
    
    def __init__(self):
        self.model = None
        if SEMANTIC_AVAILABLE:
            try:
                self.model = SentenceTransformer('data/models_cache/Sentence Embedding Model')
            except Exception as e:
                logger.warning(f"Failed to load semantic model: {e}")
    
    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        if not self.model:
            # Fallback to token-aware strategy
            return TokenAwareStrategy().chunk_text(text, config, tokenizer_manager)
        
        sentences = self._split_into_sentences(text)
        if len(sentences) <= 1:
            return sentences
        
        # Get embeddings for all sentences
        embeddings = self.model.encode(sentences)
        
        chunks = []
        current_chunk_sentences = [sentences[0]]
        
        for i in range(1, len(sentences)):
            # Calculate similarity between current sentence and chunk
            chunk_embedding = np.mean([embeddings[j] for j in range(len(current_chunk_sentences))], axis=0)
            similarity = cosine_similarity([chunk_embedding], [embeddings[i]])[0][0]
            
            # Check if adding sentence would exceed token limit
            test_chunk = " ".join(current_chunk_sentences + [sentences[i]])
            token_count = tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name)
            
            if similarity >= config.semantic_threshold and token_count <= config.max_tokens:
                current_chunk_sentences.append(sentences[i])
            else:
                # Finalize current chunk
                chunks.append(" ".join(current_chunk_sentences))
                current_chunk_sentences = [sentences[i]]
        
        if current_chunk_sentences:
            chunks.append(" ".join(current_chunk_sentences))
        
        return chunks
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Use spacy for better sentence segmentation if available."""
        if SPACY_AVAILABLE:
            try:
                import spacy
                nlp = spacy.load("en_core_web_sm")
                doc = nlp(text)
                return [sent.text.strip() for sent in doc.sents if sent.text.strip()]
            except Exception as e:
                logger.warning(f"Spacy sentence splitting failed: {e}")
        
        # Fallback to regex-based splitting
        return TokenAwareStrategy()._split_into_sentences(text)
    
    def get_name(self) -> str:
        return "semantic"


class RecursiveStrategy(ChunkingStrategy):
    """Recursive chunking with multiple separators."""
    
    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        if LANGCHAIN_AVAILABLE:
            try:
                if config.use_tokens:
                    splitter = TokenTextSplitter(
                        chunk_size=config.max_tokens,
                        chunk_overlap=config.token_overlap
                    )
                else:
                    splitter = RecursiveCharacterTextSplitter(
                        chunk_size=config.chunk_size,
                        chunk_overlap=config.chunk_overlap,
                        separators=["\n\n", "\n", ". ", " ", ""]
                    )
                return splitter.split_text(text)
            except Exception as e:
                logger.warning(f"LangChain splitter failed: {e}")
        
        # Fallback implementation
        return self._manual_recursive_split(text, config, tokenizer_manager)
    
    def _manual_recursive_split(self, text: str, config: ChunkingConfig, 
                                 tokenizer_manager: TokenizerManager) -> List[str]:
        """Manual recursive splitting implementation."""
        separators = ["\n\n", "\n", ". ", "! ", "? ", "; ", ": ", ", ", " "]
        
        def split_with_separator(text: str, sep: str) -> List[str]:
            if not sep:
                return [text]
            return [chunk.strip() for chunk in text.split(sep) if chunk.strip()]
        
        def recursive_split(text: str, sep_index: int = 0) -> List[str]:
            if sep_index >= len(separators):
                return [text] if text.strip() else []
            
            current_sep = separators[sep_index]
            parts = split_with_separator(text, current_sep)
            
            chunks = []
            current_chunk = ""
            
            for part in parts:
                test_chunk = f"{current_chunk}{current_sep}{part}" if current_chunk else part
                
                size_metric = (tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name) 
                               if config.use_tokens else len(test_chunk))
                size_limit = config.max_tokens if config.use_tokens else config.chunk_size
                
                if size_metric <= size_limit:
                    current_chunk = test_chunk
                else:
                    if current_chunk:
                        chunks.append(current_chunk)
                    
                    if size_metric > size_limit:
                        # Recursively split this part
                        chunks.extend(recursive_split(part, sep_index + 1))
                        current_chunk = ""
                    else:
                        current_chunk = part
            
            if current_chunk:
                chunks.append(current_chunk)
            
            return chunks
        
        return recursive_split(text)
    
    def get_name(self) -> str:
        return "recursive"


class HRStrategy(ChunkingStrategy):
    """HR document aware chunking (enhanced original strategy)."""
    
    def __init__(self):
        self.hr_section_patterns = [
            r'^#{1,6}\s+(.+)$', r'^\d+\.\s+(.+)$', r'^[A-Z][A-Z\s]{2,}:(?:\s|$)',
            r'^(?:Policy|Procedure)\s*\d*:?\s*(.+)$', r'^(?:Q|Question):\s*(.+)$',
            r'^(?:Section|Chapter)\s+\d+', r'^(?:ARTICLE|Article)\s+[IVX\d]+',
            r'^(?:PART|Part)\s+[IVX\d]+', r'^(?:TITLE|Title)\s+[IVX\d]+',
            r'^\d+\.\d+\.?\s+', r'^[A-Z]\.\s+', r'^\([a-z]\)\s+', r'^\([0-9]+\)\s+'
        ]
        self.compiled_patterns = [re.compile(p, re.MULTILINE | re.IGNORECASE) 
                                  for p in self.hr_section_patterns]

    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        sections = self._split_by_hr_sections(text)
        chunks = []
        current_chunk = ""
        
        for header, content in sections:
            full_section = self._join_content(header, content)
            
            size_metric = (tokenizer_manager.count_tokens(full_section, config.tokenizer_name) 
                           if config.use_tokens else len(full_section))
            size_limit = config.max_tokens if config.use_tokens else config.chunk_size
            
            test_chunk = self._join_content(current_chunk, full_section)
            test_size = (tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name) 
                         if config.use_tokens else len(test_chunk))
            
            if test_size <= size_limit:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                
                if size_metric > size_limit:
                    # Split large sections using token-aware strategy
                    token_strategy = TokenAwareStrategy()
                    section_chunks = token_strategy.chunk_text(full_section, config, tokenizer_manager)
                    chunks.extend(section_chunks)
                    current_chunk = ""
                else:
                    current_chunk = full_section
        
        if current_chunk:
            chunks.append(current_chunk)
        
        return [chunk.strip() for chunk in chunks if chunk.strip()]
    
    def _split_by_hr_sections(self, text: str) -> List[Tuple[str, str]]:
        """Enhanced HR section detection."""
        lines = text.split('\n')
        sections = []
        current_header = ""
        current_content = []
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                current_content.append(line)
                continue
            
            is_header = False
            for pattern in self.compiled_patterns:
                if pattern.match(stripped):
                    if current_header or current_content:
                        sections.append((current_header, '\n'.join(current_content)))
                    current_header = stripped
                    current_content = []
                    is_header = True
                    break
            
            if not is_header:
                current_content.append(line)
        
        if current_header or current_content:
            sections.append((current_header, '\n'.join(current_content)))
        
        return sections or [("", text)]
    
    def _join_content(self, chunk: str, new: str) -> str:
        """Join content with appropriate spacing."""
        if not chunk:
            return new
        if not new:
            return chunk
        return chunk + ("\n\n" if not chunk.endswith('\n') else "\n") + new
    
    def get_name(self) -> str:
        return "hr"


class StrategySelector:
    """Intelligent strategy selection based on document characteristics."""
    
    def select_strategy(self, text: str, config: ChunkingConfig) -> ChunkingStrategy:
        """Select optimal chunking strategy based on document analysis."""
        if config.strategy != "auto":
            return self._get_strategy_by_name(config.strategy)
        
        # Analyze document characteristics
        line_count = len(text.split('\n'))
        avg_line_length = len(text) / line_count if line_count > 0 else 0
        header_ratio = self._calculate_header_ratio(text)
        
        # Decision logic
        if config.hr_mode and header_ratio > 0.1:  # Many headers - likely structured document
            return HRStrategy()
        elif SEMANTIC_AVAILABLE and len(text) > 5000:  # Large document - use semantic
            return SemanticStrategy()
        elif avg_line_length > 100:  # Long lines - likely prose
            return TokenAwareStrategy()
        else:  # Default to recursive
            return RecursiveStrategy()
    
    def _get_strategy_by_name(self, name: str) -> ChunkingStrategy:
        """Get strategy instance by name."""
        strategies = {
            "token": TokenAwareStrategy(),
            "semantic": SemanticStrategy(),
            "recursive": RecursiveStrategy(),
            "hr": HRStrategy(),
            "sentence": TokenAwareStrategy(),  # Alias
        }
        return strategies.get(name, TokenAwareStrategy())
    
    def _calculate_header_ratio(self, text: str) -> float:
        """Calculate ratio of header lines to total lines."""
        lines = text.split('\n')
        header_patterns = [
            r'^#{1,6}\s+', r'^\d+\.\s+', r'^[A-Z][A-Z\s]{2,}:',
            r'^(?:Policy|Procedure|Section|Chapter)\s+'
        ]
        
        header_count = 0
        for line in lines:
            if any(re.match(pattern, line.strip()) for pattern in header_patterns):
                header_count += 1
        
        return header_count / len(lines) if lines else 0


class TextChunker:
    """Production-grade text chunker with enterprise features."""
    
    def __init__(self, config: Optional[ChunkingConfig] = None, chunk_size=None, chunk_overlap=None):
        self.config = config or ChunkingConfig()
        if chunk_size:
            self.config.chunk_size = chunk_size
        if chunk_overlap:
            self.config.chunk_overlap = chunk_overlap
        
        # Initialize components
        self.tokenizer_manager = TokenizerManager()
        self.strategy_selector = StrategySelector()
        self.metrics = ChunkingMetrics()
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_workers)
        
        # Performance tracking
        self._chunk_cache = {}
        self._processing_stats = {"total_documents": 0, "total_time": 0.0}
        self._last_strategy_used = 'unknown' # Initialize for metadata reporting
    
    async def chunk_document_async(self, document: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Async version of chunk_document for high-throughput scenarios."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.chunk_document, document)
    
    def chunk_document(self, document: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Enhanced document chunking with comprehensive error handling and metrics."""
        start_time = time.time()
        
        try:
            # Input validation
            content = document.get("content")
            if not isinstance(document, dict) or not isinstance(content, str):
                raise ChunkingError("Invalid document format", {"document_keys": list(document.keys())})
            
            # Text preprocessing
            content = self._normalize_text(content)
            if not content:
                logger.warning("Empty normalized content")
                return []
            
            # Check cache for repeated content
            cache_key = hashlib.md5(content.encode()).hexdigest()
            if self.config.cache_tokenization and cache_key in self._chunk_cache:
                logger.info(f"Returning cached chunks for document")
                return self._create_chunk_dicts_from_cache(document, cache_key)
            
            # Early return for small documents
            if len(content) < self.config.min_chunk_size:
                logger.info(f"Document too short to chunk: {len(content)} chars")
                chunk_dict = self._create_chunk_dict(document, content, 0, 1)
                self._update_metrics(start_time, [content], "single_chunk")
                return [chunk_dict]
            
            # Strategy selection and chunking
            strategy = self.strategy_selector.select_strategy(content, self.config)
            logger.info(f"Using strategy: {strategy.get_name()}")
            self._last_strategy_used = strategy.get_name() # Store for metadata
            
            chunks = list(self._split_text(content, strategy))
            if not chunks:
                logger.warning("No chunks produced")
                return []
            
            # Quality validation and filtering
            final_chunks = self._validate_and_filter_chunks(chunks)
            
            # Cache results
            if self.config.cache_tokenization:
                self._chunk_cache[cache_key] = final_chunks
            
            # Create final chunk dictionaries
            result = [self._create_chunk_dict(document, chunk, i, len(final_chunks)) 
                      for i, chunk in enumerate(final_chunks)]
            
            # Update metrics
            self._update_metrics(start_time, final_chunks, strategy.get_name())
            
            return result
            
        except Exception as e:
            self.metrics.errors.append(str(e))
            logger.error(f"Chunking failed: {str(e)}")
            raise ChunkingError(str(e), {"document_id": document.get("id", "unknown")}) from e
    
    def _validate_and_filter_chunks(self, chunks: List[str]) -> List[str]:
        """Validate chunks and filter out invalid ones."""
        valid_chunks = []
        
        for i, chunk in enumerate(chunks):
            chunk = chunk.strip()
            
            # Size validation
            if len(chunk) < self.config.min_chunk_size:
                self.metrics.warnings.append(f"Chunk {i} too short: {len(chunk)} chars")
                continue
            
            # Token validation (if enabled)
            if self.config.use_tokens:
                token_count = self.tokenizer_manager.count_tokens(chunk, self.config.tokenizer_name)
                if token_count > self.config.max_tokens:
                    self.metrics.warnings.append(f"Chunk {i} exceeds token limit: {token_count} tokens")
                    # Try to salvage by re-chunking
                    strategy = TokenAwareStrategy()
                    sub_chunks = strategy.chunk_text(chunk, self.config, self.tokenizer_manager)
                    valid_chunks.extend(sub_chunks)
                    continue
            
            # Content validation
            if not chunk or chunk.isspace():
                self.metrics.warnings.append(f"Chunk {i} is empty or whitespace only")
                continue
            
            valid_chunks.append(chunk)
        
        return valid_chunks
    
    def _split_text(self, text: str, strategy: ChunkingStrategy) -> Generator[str, None, None]:
        """Split text using the selected strategy."""
        try:
            chunks = strategy.chunk_text(text, self.config, self.tokenizer_manager)
            for chunk in chunks:
                if chunk and chunk.strip():
                    yield chunk.strip()
        except Exception as e:
            logger.error(f"Strategy {strategy.get_name()} failed: {e}")
            # Fallback to basic token-aware chunking
            fallback_strategy = TokenAwareStrategy()
            chunks = fallback_strategy.chunk_text(text, self.config, self.tokenizer_manager)
            for chunk in chunks:
                if chunk and chunk.strip():
                    yield chunk.strip()
    
    def _create_chunk_dict(self, doc: Dict[str, Any], chunk: str, idx: int, total: int) -> Dict[str, Any]:
        """Create enhanced chunk dictionary with additional metadata."""
        # Explicitly ensure chunk is a string
        if isinstance(chunk, list):
            chunk = " ".join(str(x) for x in chunk)
        elif not isinstance(chunk, str):
            chunk = str(chunk)
        
        # Calculate token count for the chunk
        token_count = 0
        if self.config.use_tokens:
            token_count = self.tokenizer_manager.count_tokens(chunk, self.config.tokenizer_name)
        
        
        # HR-SPECIFIC METADATA EXTRACTION (heuristic based)
        employee_categories = []
        compliance_flags = []
        effective_dates = []

        if any(k in chunk.lower() for k in ["contract", "temporary", "permanent", "full-time", "part-time"]):
            employee_categories.append("general_workforce")
        if "posh" in chunk.lower() or "prevention of sexual harassment" in chunk.lower():
            compliance_flags.append("PoSH")
        if "gdpr" in chunk.lower():
            compliance_flags.append("GDPR")
        effective_dates += re.findall(r'\b(?:effective from|start date|as of)\s+\d{1,2}[-/\s]\w+[-/\s]\d{2,4}\b', chunk, flags=re.IGNORECASE)

        # DOCUMENT RELATIONSHIP MAPPING PLACEHOLDER
        cross_references = re.findall(r'(see section\s+[\dIVX]+)', chunk, flags=re.IGNORECASE)
        hierarchy_level = chunk.count("\n#")  # Rough proxy for heading depth

        return {
            "title": f"{doc.get('title', 'Untitled')} - Part {idx + 1}",
            "text": chunk,
            "content": chunk,
            "source_file": doc.get("source_file", "Unknown"),
            "chunk_index": idx,
            "total_chunks": total,
            "chunk_size": len(chunk),
            "token_count": token_count,
            "original_title": doc.get("title", "Untitled"),
            "metadata": {
                **doc.get("metadata", {}),
                "chunking_strategy": getattr(self, '_last_strategy_used', 'unknown'),
                "tokenizer": self.config.tokenizer_name if self.config.use_tokens else None,
                "chunk_created_at": time.time(),
                "language": self.config.language,
                "word_count": len(chunk.split()),
                "sentence_count": len(self._split_into_sentences(chunk))
            }
        }
    
    def _create_chunk_dicts_from_cache(self, document: Dict[str, Any], cache_key: str) -> List[Dict[str, Any]]:
        """Create chunk dictionaries from cached chunks."""
        cached_chunks = self._chunk_cache[cache_key]
        return [self._create_chunk_dict(document, chunk, i, len(cached_chunks)) 
                for i, chunk in enumerate(cached_chunks)]
    
    def _normalize_text(self, text: str) -> str:
        """Enhanced text normalization with multilingual support."""
        # Unicode normalization
        text = unicodedata.normalize("NFKC", text)
        
        # Replace various Unicode spaces and control characters
        replacements = {
            "\u00A0": " ",      # Non-breaking space
            "\u2028": "\n",     # Line separator
            "\u2029": "\n\n",   # Paragraph separator
            "\u200B": "",      # Zero-width space
            "\u200C": "",      # Zero-width non-joiner
            "\u200D": "",      # Zero-width joiner
            "\uFEFF": "",      # Byte order mark
            "\u2060": "",      # Word joiner
            "\u00AD": "",      # Soft hyphen
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        # Normalize line endings
        text = re.sub(r'\r\n?', '\n', text)
        
        # Reduce multiple consecutive newlines
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # Normalize whitespace (but preserve intentional formatting if configured)
        if not self.config.preserve_formatting:
            text = re.sub(r'[ \t]+', ' ', text)
            text = re.sub(r' *\n *', '\n', text)
        else:
            # More conservative whitespace normalization
            text = re.sub(r'[ \t]{2,}', ' ', text)
        
        # Handle special characters based on language
        if self.config.language != "en":
            text = self._normalize_multilingual(text)
        
        return text.strip()
    
    def _normalize_multilingual(self, text: str) -> str:
        """Apply language-specific normalization."""
        # This can be extended for specific language requirements
        if self.config.language.startswith("zh"):  # Chinese
            # Handle Chinese punctuation and spacing
            text = re.sub(r'([。！？])\s+', r'\1', text)
        elif self.config.language.startswith("ja"):  # Japanese
            # Handle Japanese punctuation
            text = re.sub(r'([。！？])\s+', r'\1', text)
        elif self.config.language.startswith("ar"):  # Arabic
            # Handle RTL text normalization
            text = re.sub(r'\u200F|\u200E', '', text)  # Remove direction marks
        
        return text
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Enhanced sentence splitting with multilingual support."""
        if SPACY_AVAILABLE and self.config.language in ["en", "auto"]:
            try:
                import spacy
                nlp = spacy.load(self.config.spacy_model)
                doc = nlp(text)
                return [sent.text.strip() for sent in doc.sents if sent.text.strip()]
            except Exception as e:
                logger.warning(f"Spacy sentence splitting failed: {e}")
        
        # Fallback to enhanced regex-based splitting
        # Extended abbreviations list
        abbreviations = {
            'Dr', 'Mr', 'Mrs', 'Ms', 'Prof', 'Sr', 'Jr', 'Inc', 'Ltd', 'Corp',
            'Co', 'vs', 'etc', 'i.e', 'e.g', 'cf', 'al', 'St', 'Ave', 'Blvd',
            'Fig', 'Vol', 'No', 'pp', 'ch', 'sec', 'dept', 'univ', 'govt',
            'admin', 'assoc', 'bros', 'conf', 'corp', 'dept', 'est', 'exec'
        }
        
        # Create pattern for abbreviations
        abbrev_pattern = r'\b(?:' + '|'.join(re.escape(a) for a in abbreviations) + r')\.'
        
        # Temporarily replace abbreviations
        temp_text = re.sub(abbrev_pattern, lambda m: m.group().replace('.', '<DOT>'), text)
        
        # Enhanced sentence boundary detection
        # Handle various punctuation patterns
        sentence_endings = r'(?<=[.!?])\s+(?=[A-Z"\(\[])'
        sentences = re.split(sentence_endings, temp_text)
        
        # Restore abbreviations and clean up
        cleaned_sentences = []
        for s in sentences:
            s = s.replace('<DOT>', '.').strip()
            if s and len(s) > 3:  # Filter out very short fragments
                cleaned_sentences.append(s)
        
        return cleaned_sentences
    
    def _update_metrics(self, start_time: float, chunks: List[str], strategy_name: str):
        """Update processing metrics."""
        processing_time = time.time() - start_time
        
        self.metrics.total_chunks = len(chunks)
        self.metrics.total_characters = sum(len(chunk) for chunk in chunks)
        self.metrics.processing_time = processing_time
        self.metrics.strategy_used = strategy_name
        self.metrics.average_chunk_size = (
            self.metrics.total_characters / len(chunks) if chunks else 0
        )
        
        # Calculate token metrics if using tokens
        if self.config.use_tokens:
            total_tokens = sum(
                self.tokenizer_manager.count_tokens(chunk, self.config.tokenizer_name) 
                for chunk in chunks
            )
            self.metrics.total_tokens = total_tokens
            self.metrics.token_efficiency = (
                total_tokens / (self.config.max_tokens * len(chunks)) if chunks else 0
            )
        
        # Update global stats
        self._processing_stats["total_documents"] += 1
        self._processing_stats["total_time"] += processing_time
        self._last_strategy_used = strategy_name
        
        logger.info(f"Processed document: {len(chunks)} chunks, "
                    f"{processing_time:.2f}s, strategy: {strategy_name}")
    
    def validate_chunks(self, chunks: List[str]) -> Dict[str, Any]:
        """Enhanced chunk validation with comprehensive metrics."""
        if not chunks:
            return {
                "valid": False,
                "count": 0,
                "avg_length": 0,
                "min_length": 0,
                "max_length": 0,
                "total_length": 0,
                "issues": ["No chunks provided"],
                "token_stats": {},
                "quality_score": 0.0
            }
        
        lengths = [len(c) for c in chunks]
        token_counts = []
        
        # Calculate token statistics if enabled
        if self.config.use_tokens:
            token_counts = [
                self.tokenizer_manager.count_tokens(chunk, self.config.tokenizer_name) 
                for chunk in chunks
            ]
        
        # Identify issues
        issues = []
        for i, chunk in enumerate(chunks):
            chunk_len = len(chunk)
            
            # Size validation
            if chunk_len < self.config.min_chunk_size:
                issues.append(f"Chunk {i} too short: {chunk_len} chars")
            elif chunk_len > self.config.max_chunk_size:
                issues.append(f"Chunk {i} too long: {chunk_len} chars")
            
            # Content validation
            if not chunk.strip():
                issues.append(f"Chunk {i} is empty or whitespace only")
            
            # Token validation
            if self.config.use_tokens and token_counts:
                token_count = token_counts[i]
                if token_count > self.config.max_tokens:
                    issues.append(f"Chunk {i} exceeds token limit: {token_count} tokens")
        
        # Calculate quality score
        valid_chunks = len(chunks) - len([i for i in issues if "too short" in i or "empty" in i])
        quality_score = valid_chunks / len(chunks) if chunks else 0.0
        
        # Prepare token statistics
        token_stats = {}
        if token_counts:
            token_stats = {
                "total_tokens": sum(token_counts),
                "avg_tokens": sum(token_counts) / len(token_counts),
                "min_tokens": min(token_counts),
                "max_tokens": max(token_counts),
                "token_efficiency": sum(token_counts) / (self.config.max_tokens * len(chunks))
            }
        
        return {
            "valid": len(issues) == 0,
            "count": len(chunks),
            "avg_length": sum(lengths) / len(lengths),
            "min_length": min(lengths),
            "max_length": max(lengths),
            "total_length": sum(lengths),
            "issues": issues,
            "token_stats": token_stats,
            "quality_score": quality_score,
            "chunk_size_distribution": self._calculate_size_distribution(lengths),
            "overlap_analysis": self._analyze_overlap(chunks) if len(chunks) > 1 else {}
        }
    
    def _calculate_size_distribution(self, lengths: List[int]) -> Dict[str, int]:
        """Calculate chunk size distribution."""
        if not lengths:
            return {}
        
        min_len, max_len = min(lengths), max(lengths)
        range_size = (max_len - min_len) / 5 if max_len > min_len else 1
        
        distribution = {
            "very_small": 0,  # < 20th percentile
            "small": 0,       # 20-40th percentile
            "medium": 0,      # 40-60th percentile
            "large": 0,       # 60-80th percentile
            "very_large": 0   # > 80th percentile
        }
        
        sorted_lengths = sorted(lengths)
        p20 = sorted_lengths[len(sorted_lengths) // 5]
        p40 = sorted_lengths[2 * len(sorted_lengths) // 5]
        p60 = sorted_lengths[3 * len(sorted_lengths) // 5]
        p80 = sorted_lengths[4 * len(sorted_lengths) // 5]
        
        for length in lengths:
            if length < p20:
                distribution["very_small"] += 1
            elif length < p40:
                distribution["small"] += 1
            elif length < p60:
                distribution["medium"] += 1
            elif length < p80:
                distribution["large"] += 1
            else:
                distribution["very_large"] += 1
        
        return distribution
    
    def _analyze_overlap(self, chunks: List[str]) -> Dict[str, Any]:
        """Analyze overlap between consecutive chunks."""
        if len(chunks) < 2:
            return {}
        
        overlaps = []
        for i in range(1, len(chunks)):
            overlap = self._calculate_chunk_overlap(chunks[i-1], chunks[i])
            overlaps.append(overlap)
        
        return {
            "avg_overlap_ratio": sum(overlaps) / len(overlaps),
            "min_overlap_ratio": min(overlaps),
            "max_overlap_ratio": max(overlaps),
            "total_overlaps": len(overlaps)
        }
    
    def _calculate_chunk_overlap(self, chunk1: str, chunk2: str) -> float:
        """Calculate overlap ratio between two chunks."""
        words1 = set(chunk1.lower().split())
        words2 = set(chunk2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get comprehensive processing metrics."""
        return {
            "current_session": {
                "total_chunks": self.metrics.total_chunks,
                "total_tokens": self.metrics.total_tokens,
                "total_characters": self.metrics.total_characters,
                "processing_time": self.metrics.processing_time,
                "average_chunk_size": self.metrics.average_chunk_size,
                "token_efficiency": self.metrics.token_efficiency,
                "strategy_used": self.metrics.strategy_used,
                "errors": len(self.metrics.errors),
                "warnings": len(self.metrics.warnings)
            },
            "global_stats": self._processing_stats,
            "configuration": {
                "chunk_size": self.config.chunk_size,
                "max_tokens": self.config.max_tokens,
                "strategy": self.config.strategy,
                "tokenizer": self.config.tokenizer_name,
                "use_tokens": self.config.use_tokens,
                "language": self.config.language
            },
            "cache_stats": {
                "cached_documents": len(self._chunk_cache),
                "cache_enabled": self.config.cache_tokenization
            }
        }
    
    def clear_cache(self):
        """Clear tokenization and chunk cache."""
        self._chunk_cache.clear()
        self.tokenizer_manager._cache.clear()
        logger.info("Caches cleared")
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)