/* Notification Styles */
/* Apply consistent font family */
.duplicate-files-notification,
.notification-header,
.notification-content,
.notification-content ul,
.simple-toast {
    font-family: 'Inter', 'Segoe UI', '<PERSON>o', sans-serif;
}

/* Simple Toast Notification */
.simple-toast {
    position: fixed;
    top: 20px;
    left: 50%; /* Position at top center */
    transform: translateX(-50%) translateY(-100px); /* Start off-screen at the top */
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 400;
    letter-spacing: 0.01em;
    z-index: 10001; /* Ensure it's above everything else */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    max-width: 400px; /* Limit maximum width */
    font-family: 'Inter', 'Segoe UI', '<PERSON><PERSON>', sans-serif;
    text-align: center;
    backdrop-filter: blur(5px);
}

.simple-toast.success {
    background-color: rgba(248, 249, 250, 0.95);
    color: #2E7D32;
    border: 1px solid rgba(233, 236, 239, 0.8);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.simple-toast.error {
    background-color: rgba(248, 249, 250, 0.95);
    color: #c62828;
    border: 1px solid rgba(233, 236, 239, 0.8);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.simple-toast.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0); /* Slide in from the top */
}

.toast-close-btn {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 18px;
    cursor: pointer;
    margin-left: 16px;
    opacity: 0.7;
    padding: 0 5px;
    line-height: 1;
    font-weight: normal;
}

.toast-close-btn:hover {
    opacity: 1;
    color: #495057;
}
.duplicate-files-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    width: 300px;
    max-width: 90vw;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

.notification-header {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.notification-header i.fa-exclamation-circle {
    color: var(--error-color);
    margin-right: 8px;
}

.notification-header .close-notification {
    margin-left: auto;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-secondary);
}

.notification-content {
    padding: 10px 15px;
}

.notification-content ul {
    margin-top: 5px;
    padding-left: 20px;
    font-size: 0.75rem;
    font-weight: 400;
    color: var(--text-primary);
}

/* Animation keyframes */
@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

/* Make sure animations are applied */
.duplicate-files-notification {
    animation: slideIn 0.3s ease;
}

.duplicate-files-notification.slideOut {
    animation: slideOut 0.3s ease forwards;
}

/* Light theme for all notifications regardless of app theme */
.duplicate-files-notification {
    background-color: #ffffff !important;
    border-color: #e0e0e0 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    color: #333333 !important;
}

.notification-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #e0e0e0 !important;
    color: #333333 !important;
}

.notification-header i.fa-exclamation-circle {
    color: #f44336 !important;
}

.notification-content {
    color: #333333 !important;
}

.notification-content ul {
    color: #555555 !important;
}

.notification-header .close-notification {
    color: #777777 !important;
}

.notification-header .close-notification:hover {
    color: #333333 !important;
}

/* Override any theme-specific styles for toast notifications */
.theme-dark .simple-toast,
.theme-light .simple-toast,
[class*="theme-"] .simple-toast {
    background-color: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    color: #333333 !important;
}

.theme-dark .simple-toast.success,
.theme-light .simple-toast.success,
[class*="theme-"] .simple-toast.success {
    color: #2E7D32 !important;
}

.theme-dark .simple-toast.error,
.theme-light .simple-toast.error,
[class*="theme-"] .simple-toast.error {
    color: #c62828 !important;
}

.theme-dark .toast-close-btn,
.theme-light .toast-close-btn,
[class*="theme-"] .toast-close-btn {
    color: #6c757d !important;
}

.theme-dark .toast-close-btn:hover,
.theme-light .toast-close-btn:hover,
[class*="theme-"] .toast-close-btn:hover {
    color: #495057 !important;
}
