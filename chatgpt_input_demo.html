<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT-Style Input Demo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: #f7f7f8;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-container {
            width: 100%;
            max-width: 800px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 40px 20px;
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2rem;
        }

        .description {
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        /* ChatGPT-style Input Container */
        .chat-input-container {
            position: relative !important;
            bottom: auto !important;
            left: auto !important;
            transform: none !important;
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            z-index: auto !important;
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
        }

        .chat-input-form {
            width: 100% !important;
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .chatgpt-input-wrapper {
            display: flex !important;
            flex-direction: column !important;
            background: #ffffff !important;
            border: 1px solid #d1d5db !important;
            border-radius: 26px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            padding: 16px !important;
            gap: 12px !important;
            min-height: 80px !important;
            transition: border-color 0.15s ease, box-shadow 0.15s ease !important;
            position: relative !important;
        }

        .chatgpt-input-wrapper:focus-within {
            border-color: #9ca3af !important;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15) !important;
        }

        /* Main Input Area - Top Section */
        .chatgpt-input-area {
            width: 100% !important;
            order: 1 !important;
        }

        /* Bottom Tools Section */
        .chatgpt-bottom-tools {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            width: 100% !important;
            order: 2 !important;
        }

        .chatgpt-left-tools {
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
        }

        .chatgpt-right-tools {
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
        }

        .chatgpt-tool-btn {
            border: none !important;
            background: transparent !important;
            border-radius: 8px !important;
            display: flex !important;
            align-items: center !important;
            gap: 6px !important;
            cursor: pointer !important;
            color: #6b7280 !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            padding: 6px 8px !important;
            transition: background-color 0.15s ease, color 0.15s ease !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
            height: 32px !important;
            min-width: 32px !important;
        }

        .chatgpt-tool-btn:hover {
            background-color: #f3f4f6 !important;
            color: #374151 !important;
        }

        .chatgpt-tool-btn:active {
            background-color: #e5e7eb !important;
        }

        .chatgpt-tool-btn i {
            font-size: 14px !important;
        }

        .chatgpt-tool-btn span {
            font-size: 14px !important;
            font-weight: 500 !important;
        }

        /* Main Input Area */
        .chatgpt-input-area {
            flex: 1 !important;
            display: flex !important;
            align-items: center !important;
        }

        .chatgpt-input-area textarea {
            width: 100% !important;
            border: none !important;
            outline: none !important;
            background: transparent !important;
            resize: none !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
            font-size: 16px !important;
            line-height: 24px !important;
            color: #374151 !important;
            padding: 0 !important;
            margin: 0 !important;
            min-height: 24px !important;
            max-height: 200px !important;
            overflow-y: auto !important;
            scrollbar-width: none !important;
            -ms-overflow-style: none !important;
        }

        .chatgpt-input-area textarea::-webkit-scrollbar {
            display: none !important;
        }

        .chatgpt-input-area textarea::placeholder {
            color: #9ca3af !important;
            font-size: 16px !important;
        }

        /* Send Button - Bottom Right */
        .chatgpt-send-btn {
            width: 32px !important;
            height: 32px !important;
            border: none !important;
            background: #2d333a !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            color: #ffffff !important;
            transition: background-color 0.15s ease !important;
            padding: 0 !important;
        }

        .chatgpt-send-btn:hover:not(:disabled) {
            background: #1a1a1a !important;
        }

        .chatgpt-send-btn:disabled {
            background: #d1d5db !important;
            color: #9ca3af !important;
            cursor: not-allowed !important;
        }

        .chatgpt-send-btn svg {
            width: 16px !important;
            height: 16px !important;
        }

        .features {
            margin-top: 30px;
            text-align: left;
        }

        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            color: #666;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .features li:before {
            content: "✓";
            color: #10a37f;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Perfect ChatGPT Vertical Layout</h1>
        <p class="description">
            This is the exact ChatGPT input box with vertical layout: message input at top, tools at bottom. Only includes your 3 core functionalities: file upload, escalation, and voice input.
        </p>

        <div class="chat-input-container">
            <form class="chat-input-form">
                <div class="chatgpt-input-wrapper">
                    <!-- Main textarea at top -->
                    <div class="chatgpt-input-area">
                        <textarea placeholder="Ask anything" rows="1" autocomplete="off"></textarea>
                    </div>

                    <!-- Bottom tools section with only our 3 functionalities -->
                    <div class="chatgpt-bottom-tools">
                        <div class="chatgpt-left-tools">
                            <button type="button" class="chatgpt-tool-btn" title="Upload Documents">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button type="button" class="chatgpt-tool-btn" title="Tools">
                                <i class="fas fa-sliders-h"></i>
                                <span>Tools</span>
                            </button>
                            <button type="button" class="chatgpt-tool-btn" title="Escalate to HR" style="color: #dc3545;">
                                <i class="fas fa-exclamation-circle"></i>
                            </button>
                        </div>

                        <div class="chatgpt-right-tools">
                            <!-- Only voice input -->
                            <button type="button" class="chatgpt-tool-btn" title="Voice Input">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <!-- Send button (appears when typing) -->
                            <button type="submit" class="chatgpt-send-btn" title="Send message" disabled style="display: none;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7 11L12 6L17 11M12 18V7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="features">
            <h3>Features Maintained:</h3>
            <ul>
                <li>Message input at top (large area like ChatGPT)</li>
                <li>Tools section at bottom with your 3 functionalities</li>
                <li>File upload (plus icon)</li>
                <li>HR escalation (exclamation icon)</li>
                <li>Voice input (microphone icon)</li>
                <li>Auto-resizing textarea with "Ask anything" placeholder</li>
                <li>Send button appears only when typing</li>
                <li>Exact ChatGPT vertical layout and design</li>
                <li>Removed extra functionalities (sound wave, etc.)</li>
                <li>Responsive design for all screen sizes</li>
            </ul>
        </div>
    </div>

    <script>
        // Basic functionality demo
        const textarea = document.querySelector('textarea');
        const sendBtn = document.querySelector('.chatgpt-send-btn');

        textarea.addEventListener('input', function() {
            // Show/hide send button and enable/disable based on content
            const hasContent = this.value.trim() !== '';
            sendBtn.disabled = !hasContent;
            sendBtn.style.display = hasContent ? 'flex' : 'none';

            // Auto-resize
            this.style.height = 'auto';
            const newHeight = Math.min(this.scrollHeight, 200);
            this.style.height = newHeight + 'px';
        });

        // Handle Enter key
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                if (!sendBtn.disabled) {
                    e.preventDefault();
                    alert('Message would be sent: ' + this.value);
                    this.value = '';
                    sendBtn.disabled = true;
                    sendBtn.style.display = 'none';
                    this.style.height = 'auto';
                }
            }
        });

        // Button click handlers
        document.querySelectorAll('.chatgpt-tool-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const title = this.getAttribute('title');
                alert(title + ' clicked!');
            });
        });

        sendBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (!this.disabled) {
                alert('Message would be sent: ' + textarea.value);
                textarea.value = '';
                this.disabled = true;
                this.style.display = 'none';
                textarea.style.height = 'auto';
            }
        });
    </script>
</body>
</html>
