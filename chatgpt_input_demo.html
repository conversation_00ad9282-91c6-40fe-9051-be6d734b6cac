<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT-Style Input Demo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: #f7f7f8;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-container {
            width: 100%;
            max-width: 800px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 40px 20px;
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2rem;
        }

        .description {
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        /* ChatGPT-style Input Container */
        .chat-input-container {
            position: relative !important;
            bottom: auto !important;
            left: auto !important;
            transform: none !important;
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            z-index: auto !important;
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
        }

        .chat-input-form {
            width: 100% !important;
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .chatgpt-input-wrapper {
            display: flex !important;
            align-items: flex-end !important;
            background: #ffffff !important;
            border: 1px solid #d1d5db !important;
            border-radius: 26px !important;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1) !important;
            padding: 12px 16px !important;
            gap: 8px !important;
            min-height: 52px !important;
            transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
        }

        .chatgpt-input-wrapper:focus-within {
            border-color: #10a37f !important;
            box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.1), 0 0 15px rgba(0, 0, 0, 0.1) !important;
        }

        /* Left Action Buttons */
        .input-left-actions {
            display: flex !important;
            align-items: center !important;
            gap: 4px !important;
            flex-shrink: 0 !important;
        }

        .chatgpt-action-btn {
            width: 32px !important;
            height: 32px !important;
            border: none !important;
            background: transparent !important;
            border-radius: 8px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            color: #6b7280 !important;
            font-size: 16px !important;
            transition: background-color 0.2s ease, color 0.2s ease !important;
            padding: 0 !important;
        }

        .chatgpt-action-btn:hover {
            background-color: #f3f4f6 !important;
            color: #374151 !important;
        }

        .chatgpt-action-btn:active {
            background-color: #e5e7eb !important;
        }

        /* Main Input Area */
        .chatgpt-input-area {
            flex: 1 !important;
            display: flex !important;
            align-items: center !important;
        }

        .chatgpt-input-area textarea {
            width: 100% !important;
            border: none !important;
            outline: none !important;
            background: transparent !important;
            resize: none !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
            font-size: 16px !important;
            line-height: 24px !important;
            color: #374151 !important;
            padding: 0 !important;
            margin: 0 !important;
            min-height: 24px !important;
            max-height: 200px !important;
            overflow-y: auto !important;
            scrollbar-width: none !important;
            -ms-overflow-style: none !important;
        }

        .chatgpt-input-area textarea::-webkit-scrollbar {
            display: none !important;
        }

        .chatgpt-input-area textarea::placeholder {
            color: #9ca3af !important;
        }

        /* Right Action Buttons */
        .input-right-actions {
            display: flex !important;
            align-items: center !important;
            gap: 4px !important;
            flex-shrink: 0 !important;
        }

        .arrow-controls {
            display: flex !important;
            flex-direction: column !important;
            gap: 2px !important;
        }

        .chatgpt-arrow-btn {
            width: 24px !important;
            height: 20px !important;
            border: none !important;
            background: transparent !important;
            border-radius: 4px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            color: #6b7280 !important;
            font-size: 12px !important;
            transition: background-color 0.2s ease, color 0.2s ease !important;
            padding: 0 !important;
        }

        .chatgpt-arrow-btn:hover {
            background-color: #f3f4f6 !important;
            color: #374151 !important;
        }

        .chatgpt-arrow-btn:active {
            background-color: #e5e7eb !important;
        }

        /* Send Button */
        .chatgpt-send-btn {
            width: 32px !important;
            height: 32px !important;
            border: none !important;
            background: #000000 !important;
            border-radius: 8px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            color: #ffffff !important;
            transition: background-color 0.2s ease !important;
            padding: 0 !important;
            margin-left: 4px !important;
        }

        .chatgpt-send-btn:hover:not(:disabled) {
            background: #1a1a1a !important;
        }

        .chatgpt-send-btn:disabled {
            background: #f3f4f6 !important;
            color: #9ca3af !important;
            cursor: not-allowed !important;
        }

        .chatgpt-send-btn svg {
            width: 16px !important;
            height: 16px !important;
        }

        .features {
            margin-top: 30px;
            text-align: left;
        }

        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            color: #666;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .features li:before {
            content: "✓";
            color: #10a37f;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>ChatGPT-Style Message Input</h1>
        <p class="description">
            This is an exact replica of ChatGPT's message input box with all your existing functionalities maintained.
        </p>

        <div class="chat-input-container">
            <form class="chat-input-form">
                <div class="chatgpt-input-wrapper">
                    <!-- Left action buttons -->
                    <div class="input-left-actions">
                        <button type="button" class="chatgpt-action-btn" title="Upload Documents">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <button type="button" class="chatgpt-action-btn" title="Voice Input">
                            <i class="fas fa-microphone"></i>
                        </button>
                        <button type="button" class="chatgpt-action-btn" title="Record" style="color: #dc3545;">
                            <i class="fas fa-circle"></i>
                        </button>
                        <div class="chatgpt-action-btn" title="Escalate to HR" style="color: #dc3545;">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                    </div>

                    <!-- Main textarea -->
                    <div class="chatgpt-input-area">
                        <textarea placeholder="Message ChatGPT" rows="1" autocomplete="off"></textarea>
                    </div>

                    <!-- Right actions -->
                    <div class="input-right-actions">
                        <div class="arrow-controls">
                            <button type="button" class="chatgpt-arrow-btn" title="Previous">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button type="button" class="chatgpt-arrow-btn" title="Next">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <button type="submit" class="chatgpt-send-btn" title="Send message" disabled>
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7 11L12 6L17 11M12 18V7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <div class="features">
            <h3>Features Maintained:</h3>
            <ul>
                <li>File upload functionality (paperclip icon)</li>
                <li>Voice input and recording capabilities</li>
                <li>HR escalation feature</li>
                <li>Arrow controls for message navigation</li>
                <li>Auto-resizing textarea</li>
                <li>Send button with proper states</li>
                <li>Responsive design for all screen sizes</li>
                <li>ChatGPT-style visual design and animations</li>
            </ul>
        </div>
    </div>

    <script>
        // Basic functionality demo
        const textarea = document.querySelector('textarea');
        const sendBtn = document.querySelector('.chatgpt-send-btn');

        textarea.addEventListener('input', function() {
            // Enable/disable send button based on content
            const hasContent = this.value.trim() !== '';
            sendBtn.disabled = !hasContent;

            // Auto-resize
            this.style.height = 'auto';
            const newHeight = Math.min(this.scrollHeight, 200);
            this.style.height = newHeight + 'px';
        });

        // Handle Enter key
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                if (!sendBtn.disabled) {
                    e.preventDefault();
                    alert('Message would be sent: ' + this.value);
                    this.value = '';
                    sendBtn.disabled = true;
                    this.style.height = 'auto';
                }
            }
        });

        // Button click handlers
        document.querySelectorAll('.chatgpt-action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const title = this.getAttribute('title');
                alert(title + ' clicked!');
            });
        });

        sendBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (!this.disabled) {
                alert('Message would be sent: ' + textarea.value);
                textarea.value = '';
                this.disabled = true;
                textarea.style.height = 'auto';
            }
        });
    </script>
</body>
</html>
