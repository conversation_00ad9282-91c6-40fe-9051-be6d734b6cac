# Advanced Multi-Model RAG Chatbot

An advanced HR assistant chatbot that uses Retrieval-Augmented Generation (RAG) to provide accurate responses based on company HR documents.

## Features

- **Local Document Processing**: Process and index HR documents (PDF, DOCX, TXT, MD) without cloud dependencies
- **Vector Search**: Fast and accurate retrieval of relevant information using FAISS
- **Conversational AI**: Natural language understanding and generation using Groq's Llama 3 model
- **Voice Interaction**: Speech-to-text and text-to-speech capabilities
- **Modern UI/UX**: Responsive design with multiple themes and advanced features
- **Source Attribution**: View the sources used to generate responses
- **Document Upload**: Add new documents through the UI
- **Chat History**: Save and manage conversation history
- **Export Functionality**: Export conversations for record-keeping
- **Email Escalation**: Automatically escalate unanswered HR questions to designated HR personnel

## Architecture

The chatbot uses a modular architecture with the following components:

1. **Document Processing**: Extract text from documents, chunk into manageable pieces, and generate embeddings
2. **Vector Database**: Store and retrieve document embeddings for similarity search
3. **Retrieval System**: Find relevant document chunks based on user queries
4. **LLM Chain**: Generate responses using retrieved context and conversation history
5. **Conversation Management**: Track and manage conversation history
6. **Speech Processing**: Convert between speech and text
7. **Web Interface**: Modern, responsive UI for user interaction

## Getting Started

### Prerequisites

- Python 3.9+
- Virtual environment (recommended)

### Installation

1. Clone the repository
2. Create and activate a virtual environment:
   ```
   conda create -p venv python=3.10 -y
   conda activate venv/
   ```
3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
4. Set up environment variables:
   - Create a `.env` file with your API keys and configuration:
     ```
     # Required
     GROQ_API_KEY=your_api_key_here

     # Optional - Email Escalation (if enabled)
     SMTP_SERVER=smtp.gmail.com
     SMTP_PORT=587
     SMTP_USERNAME=<EMAIL>
     SMTP_PASSWORD=your_app_password
     SENDER_EMAIL=<EMAIL>
     HR_EMAILS=<EMAIL>,<EMAIL>
     ENABLE_EMAIL_ESCALATION=true
     ```

### Running the Chatbot

1. Start the application:
   ```
   python app.py
   ```
2. Open your browser and navigate to `http://localhost:5000`

## Adding HR Documents

Place your HR documents in the `Hr Files` directory. The following formats are supported:
- PDF (`.pdf`)
- Microsoft Word (`.docx`)
- Text files (`.txt`)
- Markdown files (`.md`)

The documents will be automatically processed and indexed when the application starts.

## Customization

- **Themes**: Choose from light, dark, blue, or green themes
- **Voice Settings**: Enable or disable voice responses
- **Model Settings**: Adjust model parameters in `config.py`

## Development

### Project Structure

```
/advanced_rag_chatbot
│
├── app.py                                  # Main application entry point for the Flask web server.
├── config.py                               # Configuration settings for the application, including model paths, API keys, and various thresholds.
│
├── /src
│   ├── /chain
│   │   ├── chain_builder.py                # Builds and executes the LLM chain for generating responses, integrating prompt templates and context.
│   │   └── prompt_templates.py             # Defines various prompt templates used by the LLM for different conversational scenarios.
│   ├── /core
│   │   └── resources.py                    # Manages shared resources like the unified model and embedding tokenizer, handling lazy loading, warm-up, and cleanup.
│   ├── /database
│   │   ├── conversation_store.py           # Manages saving and retrieving conversation history using a `ConversationModel`.
│   │   ├── user_db.py                      # Manages user-related data, including authentication and user profiles.
│   │   └── vector_store.py                 # Handles interactions with the Qdrant vector database for storing and retrieving document embeddings.
│   ├── /document_processing
│   │   ├── embedding_generator.py          # Generates document embeddings using either SentenceTransformers or Hugging Face Transformers.
│   │   ├── file_processor.py               # Extracts text from various file types (PDF, DOCX, TXT, MD, CSV, XLS, XLSX, PPTX, HTML, HTM, MSG, JPG, JPEG, PNG).
│   │   ├── text_chunker.py                 # Splits documents into chunks for embedding and retrieval, with options for HR-specific modes.
│   │   ├── training_pipeline.py            # Implements an end-to-end training pipeline for document processing, including database and vector store updates.
│   │   └── version_control.py              # Manages document versioning, re-indexing in the Qdrant vector store, and document backups.
│   ├── /intent
│   │   └── intent_classifier.py            # Contains the `AutoTemperatureCalibrator` class for optimizing model temperature using various methods and metrics.
│   ├── /ner
│   │   └── entity_extractor.py             # Performs Named Entity Recognition (NER) and Span Categorization for HR queries using spaCy with Hugging Face transformers.
│   ├── /retrieval
│   │   ├── context_builder.py              # Builds context from vector search results, managing token budgets and query enhancement.
│   │   └── vector_search.py                # Performs vector similarity search for document retrieval using Qdrant.
│   ├── /speech
│   │   ├── speech_to_text.py               # Converts speech to text using the Whisper model.
│   │   └── text_to_speech.py               # Converts text to speech using `pyttsx3`.
│   ├── /user_authentication
│   │   └── user_authorisation.py           # Provides authentication services, including password hashing, 2FA, user registration, login, and JWT token management.
│   └── /utils
│       ├── api_status.py                   # Utility for checking the operational status of external APIs, specifically Groq.
│       ├── email_service.py                # Handles sending emails, including escalation emails to HR personnel.
│       ├── error_handler.py                # Provides utilities for safe function execution and standardized error response formatting.
│       ├── faiss_utils.py                  # Utility functions for FAISS initialization and configuration, handling AVX2 module and index creation.
│       ├── logger.py                       # Configures and provides logging functionality for the application.
│       └── logging_config.py               # Centralized logging configuration for the application, ensuring UTF-8 encoding and preventing duplicate logs.
│
├── /static                                 # Static files for the web interface (CSS, JavaScript, images).
│   ├── css/
│   │   └── styles.css                      # Main stylesheet for the application's UI.
│   ├── js/
│   │   └── app.js                          # Main JavaScript file for front-end interactivity.
│   └── images/                             # Image assets used in the web interface.
│
├── /templates                              # HTML templates for the web interface.
│   └── index.html                          # Main application page.
│
│
├── /data                                   # Contains all persistent data for the application.
│   ├── config/ - Configuration files.
│   │   └── intents.json - Defines intents for the chatbot.
│   ├── db/ - Database files.
│   │   ├── chatbot.db - SQLite database for chatbot-related data.
│   │   ├── convo.db - SQLite database for conversation history.
│   │   ├── documents.db - SQLite database for document storage.
│   │   └── users.db - SQLite database for user-related data.
│   ├── processed/ - Processed versions of HR documents.
│   ├── raw_files/ - Original, unprocessed HR documents.
│   │   ├── Code-of-Conduct-Policy-Ziantrix.docx
│   │   ├── Dress-Code-Policy-Ziantrix.docx
│   │   ├── Employee-Referral-Policy-Ziantrix.md
│   │   ├── Employee-Termination-Policy-Ziantrix.docx
│   │   ├── Gratuity-Policy-Ziantrix.docx
│   │   ├── Health-and-Safety-Policy-Ziantrix.txt
│   │   ├── Leave-Policy-Ziantrix.txt
│   │   ├── MoonLight Policy-Ziantrix.docx
│   │   ├── Posh-Policy-Ziantrix.docx
│   │   ├── Promotion-Policy-Ziantrix.docx
│   │   ├── Recruitment-Policy-Ziantrix.txt
│   │   ├── Social-Media-Policy-Ziantrix.docx
│   │   ├── Whistleblower-Policy-Ziantrix.txt
│   │   └── Work-from-Home-Policy-Ziantrix.docx
│   └── training/ - Training data for models.
│       └── intent_training_data.jsonl - JSONL file containing intent training examples.
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
