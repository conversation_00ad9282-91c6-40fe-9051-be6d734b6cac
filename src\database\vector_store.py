import os
import json
import numpy as np
import uuid
import time
from typing import List, Dict, Any, Optional
from qdrant_client import QdrantClient
from qdrant_client.models import (
    Distance, VectorParams, PointStruct, CollectionStatus,
    Filter, FieldCondition, MatchValue
)
from ..utils.logger import get_logger
from dotenv import load_dotenv

load_dotenv()

QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")
QDRANT_URL = os.getenv("QDRANT_URL")
QDRANT_COLLECTION_NAME = os.getenv("QDRANT_COLLECTION_NAME")
VECTOR_DIMENSION = 768

logger = get_logger(__name__)

class VectorStoreError(Exception):
    """Custom exception for vector store operations."""
    pass

class QdrantVectorStore:
    def __init__(self):
        self.client: Optional[QdrantClient] = None
        self._connect()
        self._setup_collection()

    def _connect(self):
        """Initialize Qdrant client."""
        try:
            self.client = QdrantClient(
                url=QDRANT_URL,
                api_key=QDRANT_API_KEY
            )
            logger.info("🔌 Connected to Qdrant vector store.")
        except Exception as e:
            logger.error("❌ Failed to connect to Qdrant", extra={'error': str(e)})
            raise VectorStoreError("Could not connect to Qdrant.")

    def _validate_client(self):
        if not self.client:
            raise VectorStoreError("Qdrant client is not initialized.")

    def _setup_collection(self):
        """Ensure the collection is set up correctly."""
        self._validate_client()
        try:
            if not self.client.collection_exists(QDRANT_COLLECTION_NAME):
                logger.info(f"📦 Creating new collection: {QDRANT_COLLECTION_NAME}")
                self.client.recreate_collection(
                    collection_name=QDRANT_COLLECTION_NAME,
                    vectors_config=VectorParams(size=VECTOR_DIMENSION, distance=Distance.COSINE)
                )
            else:
                logger.info(f"📂 Collection already exists: {QDRANT_COLLECTION_NAME}")
        except Exception as e:
            logger.error("❌ Failed to setup Qdrant collection", extra={'error': str(e)})
            raise VectorStoreError("Failed to create collection.")

    def _generate_unique_id(self, base_id: Optional[str] = None) -> str:
        """Generate a unique ID for a point."""
        if base_id:
            return f"{base_id}_{int(time.time() * 1000000)}"
        return str(uuid.uuid4())

    def upload(self, documents: List[Dict[str, Any]], embeddings: np.ndarray):
        """Upload document embeddings to Qdrant."""
        self._validate_client()

        if len(documents) != len(embeddings):
            raise ValueError("Mismatch between number of documents and embeddings.")

        try:
            info = self.get_collection_info()
            current_count = info.get('points_count', 0)

            points = []
            for idx, (document, embedding) in enumerate(zip(documents, embeddings)):
                point_id = self._generate_unique_id(document.get('chunk_id'))

                # Inject content field if missing
                if "content" not in document:
                    if "text" in document:
                        document["content"] = document["text"]
                    else:
                        logger.warning("⚠️ Missing 'content' in document payload. Skipping this point.", extra={'chunk_id': document.get('chunk_id')})
                        continue

                if hasattr(embedding, 'tolist'):
                    vector = embedding.tolist()
                elif isinstance(embedding, (list, tuple)):
                    vector = list(embedding)
                else:
                    vector = [float(x) for x in embedding]

                points.append(PointStruct(
                    id=point_id,
                    vector=vector,
                    payload=document
                ))

            logger.info("📤 Preparing upload", extra={
                'points_to_upload': len(points),
                'current_collection_size': current_count,
                'sample_point_id': points[0].id if points else None
            })

            batch_size = 100
            total_uploaded = 0

            for i in range(0, len(points), batch_size):
                batch = points[i:i + batch_size]
                result = self.client.upsert(
                    collection_name=QDRANT_COLLECTION_NAME,
                    points=batch,
                    wait=True
                )
                total_uploaded += len(batch)
                logger.info(f"✅ Batch {i//batch_size + 1} uploaded", extra={
                    'batch_size': len(batch),
                    'total_uploaded': total_uploaded,
                    'operation_id': result.operation_id,
                    'status': result.status.value if result.status else 'unknown'
                })

            updated_info = self.get_collection_info()
            final_count = updated_info.get('points_count', 0)

            logger.info("📊 Upload verification", extra={
                'points_before': current_count,
                'points_after': final_count,
                'points_added': final_count - current_count,
                'expected_added': len(points),
                'upload_successful': (final_count - current_count) == len(points)
            })

            if (final_count - current_count) != len(points):
                logger.warning("⚠️ Upload count mismatch", extra={
                    'expected': len(points),
                    'actual': final_count - current_count
                })

        except Exception as e:
            logger.error("❌ Upload to Qdrant failed", extra={
                'error': str(e),
                'documents_attempted': len(documents)
            }, exc_info=True)
            raise VectorStoreError(f"Upload failed: {e}")

    def search(self, query_embedding: np.ndarray, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for similar documents using query embedding."""
        self._validate_client()

        logger.info("🔍 Searching vector store", extra={
            'top_k': top_k,
            'query_shape': query_embedding.shape if hasattr(query_embedding, 'shape') else 'unknown'
        })

        try:
            if hasattr(query_embedding, 'ndim') and query_embedding.ndim > 1:
                vector = query_embedding[0] if query_embedding.shape[0] == 1 else query_embedding.flatten()
            else:
                vector = query_embedding

            if hasattr(vector, 'astype'):
                vector = vector.astype(float).tolist()
            elif isinstance(vector, (list, tuple)):
                vector = [float(x) for x in vector]
            else:
                vector = [float(x) for x in vector]

            results = self.client.search(
                collection_name=QDRANT_COLLECTION_NAME,
                query_vector=vector,
                limit=top_k
            )

            search_results = [
                {
                    "content": r.payload.get("content", ""),
                    "score": r.score,
                    "source_file": r.payload.get("source_file", ""),
                    "chunk_id": r.payload.get("chunk_id", ""),
                    "chunk_index": r.payload.get("chunk_index", ""),
                    "document_name": r.payload.get("document_name", ""),
                    "title": r.payload.get("title", ""),
                    "timestamp": r.payload.get("timestamp", ""),
                    "point_id": r.id
                }
                for r in results
            ]

            logger.info("✅ Search completed", extra={
                'results_found': len(search_results),
                'top_score': search_results[0]['score'] if search_results else 0
            })

            return search_results

        except Exception as e:
            logger.error("❌ Search failed", extra={
                'error': str(e),
                'collection': QDRANT_COLLECTION_NAME
            }, exc_info=True)
            raise VectorStoreError(f"Search failed: {e}")

    def get_collection_info(self) -> Dict[str, Any]:
        """Get metadata about the collection."""
        self._validate_client()
        try:
            if not self.client.collection_exists(QDRANT_COLLECTION_NAME):
                logger.warning(f"⚠️ Collection does not exist: {QDRANT_COLLECTION_NAME}")
                return {"exists": False}

            info = self.client.get_collection(QDRANT_COLLECTION_NAME)
            return {
                "exists": True,
                "points_count": info.points_count,
                "vector_size": info.config.params.vectors.size,
                "distance": info.config.params.vectors.distance.value,
                "collection_name": QDRANT_COLLECTION_NAME,
                "status": info.status.value if info.status else "unknown"
            }

        except Exception as e:
            logger.error("❌ Failed to get collection info", extra={'error': str(e)}, exc_info=True)
            return {"exists": False, "error": str(e)}

    def clear(self) -> None:
        """Delete and recreate the collection (destructive)."""
        self._validate_client()

        try:
            if self.client.collection_exists(QDRANT_COLLECTION_NAME):
                self.client.delete_collection(QDRANT_COLLECTION_NAME)
                logger.info(f"✅ Deleted collection: {QDRANT_COLLECTION_NAME}")
                self._setup_collection()
                logger.info(f"✅ Recreated collection: {QDRANT_COLLECTION_NAME}")
            else:
                logger.info(f"ℹ️ Collection doesn't exist: {QDRANT_COLLECTION_NAME}")

        except Exception as e:
            logger.error("❌ Failed to clear collection", extra={'error': str(e)}, exc_info=True)
            raise VectorStoreError(f"Clear failed: {e}")

    def health_check(self) -> bool:
        """Check if Qdrant is available and collection exists."""
        try:
            self._validate_client()
            collections = self.client.get_collections()
            exists = QDRANT_COLLECTION_NAME in [c.name for c in collections.collections]
            return exists
        except Exception as e:
            logger.error("❌ Health check failed", extra={'error': str(e)}, exc_info=True)
            return False
