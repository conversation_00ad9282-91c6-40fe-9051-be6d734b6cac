"""
Utility for checking API service status.
"""
import time
import requests
from typing import Dict, Any, Optional
import threading
import os
from dotenv import load_dotenv
from src.utils.logger import get_logger

load_dotenv()

logger = get_logger(__name__)

class APIStatusChecker:
    """Check and monitor API service status."""

    def __init__(self):
        self._lock = threading.Lock()

    def check_groq_status(self, force_refresh: bool = False) -> Optional[Dict[str, Any]]:
        """Check Groq API status via test prompt."""

        status = {
            "operational": True,
            "message": "Service appears to be operational",
            "last_checked": time.time()
        }

        try:
            response = requests.post(
                os.getenv("GROQ_API_ENDPOINT"),
                headers={
                    "Authorization": f"Bearer {os.getenv('GROQ_API_KEY')}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": os.getenv("GROQ_MODEL"),
                    "messages": [{"role": "user", "content": "ping"}],
                    "temperature": 0,
                    "max_tokens": 1
                },
                timeout=5
            )

            if response.status_code == 200:
                status["operational"] = True
                status["message"] = "Groq API is operational"
            else:
                status["operational"] = False
                status["message"] = f"Groq API returned status code {response.status_code}"
                status["status_code"] = response.status_code

        except requests.RequestException as e:
            status["operational"] = False
            status["message"] = f"Error connecting to Groq API: {str(e)}"
            logger.warning(f"Groq API status check failed: {e}")

        with self._lock:
            return status

    def is_groq_operational(self) -> bool:
        """Return True if Groq API is operational, safely."""
        status = self.check_groq_status(force_refresh=True)
        if not isinstance(status, dict):
            logger.warning("Groq status check returned non-dict response")
            return False
        return status.get("operational", False)


if __name__ == "__main__":
    api_status_checker = APIStatusChecker()
    status = api_status_checker.check_groq_status(force_refresh=True)
    print("\n=== Groq Health Check ===")
    print(status)

