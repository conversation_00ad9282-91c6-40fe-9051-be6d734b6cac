/**
 * Header UI Cleanup
 * Removes header line entirely (pseudo and dynamic)
 * Matches clean, line-free UI behavior
 */

/* Remove any default borders from the chat header */
.chat-header {
    border-bottom: none !important;
    border-top: none !important;
}

/* Completely disable any header line styles and elements */
.app-container::after,
.chat-header::after,
.chat-header::before,
.sidebar::after,
#header-line-element {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    height: 0 !important;
    background: none !important;
    content: none !important;
    position: static !important;
}

/* Ensure the header content is properly positioned above all */
.header-content {
    position: relative;
    z-index: 11;
}

/* Disable color settings for header line in both themes */
.theme-dark .sidebar::after,
.theme-dark #header-line-element,
.theme-light .sidebar::after,
.theme-light #header-line-element {
    background: none !important;
}

/* Mobile responsive adjustments - ensure header line is hidden */
@media (max-width: 768px) {
    .sidebar::after,
    #header-line-element {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        height: 0 !important;
        background: none !important;
        content: none !important;
    }
}
