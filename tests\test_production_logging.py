#!/usr/bin/env python3
"""
Production-Grade Logging Test Suite

This script demonstrates all the production-grade features of the logging system:
- Concurrent logging with file locking
- PII masking and data sanitization
- Performance monitoring
- Log compression and archival
- Security features
- Health monitoring
- Error recovery
"""

import time
import threading
import random
import string
from pathlib import Path
from src.utils.logger import (
    initialize_logging, 
    get_logger, 
    shutdown_logging,
    LoggingConfig,
    LogLevel,
    LogFormat,
    RotationType
)

def generate_test_data():
    """Generate test data with sensitive information."""
    return {
        "user_id": "12345",
        "email": "<EMAIL>",
        "credit_card": "1234-5678-9012-3456",
        "ssn": "***********",
        "phone": "************",
        "password": "secret_password_123",
        "api_key": "sk_test_1234567890abcdef",
        "normal_data": "This is normal data that should not be masked"
    }

def test_basic_logging(logger):
    """Test basic logging functionality."""
    print("🔍 Testing basic logging...")
    
    logger.debug("Debug message - should not appear in INFO level")
    logger.info("Info message - should appear")
    logger.warning("Warning message - should appear")
    logger.error("Error message - should appear")
    logger.critical("Critical message - should appear")

def test_pii_masking(logger):
    """Test PII masking functionality."""
    print("🔒 Testing PII masking...")
    
    test_data = generate_test_data()
    
    # Test with sensitive data
    logger.info(f"User data: {test_data}")
    logger.info("Processing payment with card: 1234-5678-9012-3456")
    logger.info("Contact info: <EMAIL>, ************")
    logger.info("SSN: ***********")

def test_concurrent_logging(logger):
    """Test concurrent logging with multiple threads."""
    print("🔄 Testing concurrent logging...")
    
    def worker(worker_id, message_count):
        worker_logger = get_logger(f"worker_{worker_id}")
        for i in range(message_count):
            worker_logger.info(f"Worker {worker_id} - Message {i}")
            time.sleep(random.uniform(0.01, 0.05))
    
    threads = []
    for i in range(10):
        thread = threading.Thread(
            target=worker, 
            args=(i, 20)
        )
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()

def test_performance_logging(logger):
    """Test performance monitoring."""
    print("⚡ Testing performance monitoring...")
    
    # Generate high-volume logging
    for i in range(1000):
        logger.info(f"Performance test message {i}")
        if i % 100 == 0:
            time.sleep(0.01)  # Small delay to simulate real usage

def test_error_scenarios(logger):
    """Test error handling and recovery."""
    print("🚨 Testing error scenarios...")
    
    # Test various error conditions
    try:
        raise ValueError("Test error for logging")
    except Exception as e:
        logger.error(f"Caught exception: {e}", exc_info=True)
    
    # Test with malformed data
    logger.info("Testing with special characters: 🚀🌟💻")
    logger.info("Testing with unicode: 中文测试")

def test_structured_logging(logger):
    """Test structured logging with context."""
    print("📊 Testing structured logging...")
    
    # Log with extra context
    logger.info("User action completed", extra={
        "user_id": "12345",
        "action": "login",
        "ip_address": "***********",
        "duration_ms": 150,
        "success": True
    })
    
    logger.warning("High memory usage detected", extra={
        "memory_usage_mb": 1024,
        "threshold_mb": 800,
        "component": "database"
    })

def test_log_rotation(logger):
    """Test log rotation functionality."""
    print("🔄 Testing log rotation...")
    
    # Generate enough messages to trigger rotation
    for i in range(5000):
        logger.info(f"Rotation test message {i} - " + "x" * 100)
        if i % 500 == 0:
            time.sleep(0.01)

def main():
    """Run comprehensive production logging tests."""
    print("🚀 Starting Production-Grade Logging Test Suite")
    print("=" * 60)
    
    # Create production configuration
    config = LoggingConfig(
        log_dir=Path("logs"),
        log_filename="production_test.log",
        rotation_type=RotationType.BOTH,
        max_bytes=10 * 1024 * 1024,  # 10MB
        backup_count=5,
        default_level=LogLevel.INFO,
        file_level=LogLevel.DEBUG,
        console_level=LogLevel.INFO,
        file_format=LogFormat.JSON,
        console_format=LogFormat.TEXT,
        enable_compression=True,
        enable_encryption=False,  # Disable for testing
        enable_pii_masking=True,
        enable_performance_monitoring=True,
        performance_alert_threshold_ms=50.0,
        queue_size=5000
    )
    
    print("📋 Configuration:")
    print(f"  - Log directory: {config.log_dir}")
    print(f"  - Log file: {config.log_filename}")
    print(f"  - Rotation: {config.rotation_type.value}")
    print(f"  - Max bytes: {config.max_bytes / 1024 / 1024:.1f}MB")
    print(f"  - PII masking: {config.enable_pii_masking}")
    print(f"  - Performance monitoring: {config.enable_performance_monitoring}")
    print()
    
    # Initialize logging
    print("🔧 Initializing logging system...")
    log_manager = initialize_logging(config)
    logger = get_logger(__name__)
    
    try:
        # Run all tests
        test_basic_logging(logger)
        test_pii_masking(logger)
        test_concurrent_logging(logger)
        test_performance_logging(logger)
        test_error_scenarios(logger)
        test_structured_logging(logger)
        test_log_rotation(logger)
        
        # Get health status
        print("\n📈 Getting health status...")
        health = log_manager.get_health_status()
        print(f"Health Status: {health}")
        
        # Get performance metrics if available
        if hasattr(log_manager, 'performance_monitor'):
            perf_metrics = log_manager.performance_monitor.get_performance_metrics()
            print(f"Performance Metrics: {perf_metrics}")
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Test suite failed: {e}", exc_info=True)
    
    finally:
        # Shutdown logging
        print("\n🔧 Shutting down logging system...")
        shutdown_logging()
        print("✅ Logging system shutdown complete")

if __name__ == "__main__":
    main() 