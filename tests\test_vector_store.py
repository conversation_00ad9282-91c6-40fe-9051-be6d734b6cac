from src.database.vector_store import QdrantVectorStore
import numpy as np

# Sample document and embedding
docs = [
    {
        "content": "This is a test HR document.",
        "source_file": "sample.pdf",
        "chunk_id": "abc123",
        "chunk_index": 0,
        "document_name": "HR Policy",
        "title": "HR Policy Title",
        "timestamp": "2025-06-16T10:00:00Z"
    }
]

# Use dummy 768-dim vector (match your model output!)
embeds = np.random.rand(1, 768)

vs = QdrantVectorStore()

# Upload
vs.upload(docs, embeds)

# Search
results = vs.search(embeds[0], top_k=1)
print("🔍 Search Results:", results)

# Health Check
print("🩺 Health:", vs.health_check())

# Sample Docs
print("📋 Samples:", vs.get_sample_documents())
