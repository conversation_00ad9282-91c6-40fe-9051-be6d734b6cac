# ───────────── Web Frameworks ─────────────
flask==3.0.0
flask-cors==4.0.0
python-dotenv==1.0.0
chardet

# ───────────── Langchain & Integrations ─────────────
# Compatible versions that satisfy all dependency requirements
langchain==0.3.25  # Latest stable version (supports numpy 2.0+)
langchain-core>=0.3.58  # Required by langchain 0.3.25 (was the issue!)
langchain-community==0.3.25  # Matches langchain version
langsmith  # Latest version
langchain-groq  # Let pip resolve compatible version

# ───────────── NLP & Embeddings ─────────────
sentence-transformers>=2.6.0
huggingface-hub>=0.23.0
torch==2.3.0
transformers>=4.45.0  # Updated to support newer langchain
rapidfuzz
spacy-transformers==1.3.9
spacy==3.8.7
SpeechRecognition
tiktoken
jwt

# ───────────── Vector Databases ─────────────
faiss-cpu==1.11.0
qdrant-client

# ───────────── Core Libraries ─────────────
numpy>=1.26.0,<3.0.0  # Flexible range that works with all dependencies
pandas>=2.0.3,<3.0.0
scikit-learn>=1.3.0
joblib>=1.3.2

# ───────────── File Parsing ─────────────
python-magic==0.4.27
python-magic-bin==0.4.14
PyPDF2==3.0.1
python-docx==1.0.1
openpyxl==3.1.2
pymupdf
python-pptx
extract-msg
beautifulsoup4
pytesseract
pillow
bcrypt
pyotp
accelerate
soundfile
sounddevice
optimum
bitsandbytes
langdetect
concurrent-log-handler

# ───────────── Utilities ─────────────
markdown==3.5
python-dateutil==2.8.2
pytz==2023.3
requests==2.31.0
tqdm==4.66.1

# ───────────── Testing ─────────────
pytest==7.4.3
pytest-cov==4.1.0
pytest-asyncio

# ───────────── Dev & Linting ─────────────
black==23.11.0
flake8==6.1.0
isort==5.12.0
mypy==1.7.0