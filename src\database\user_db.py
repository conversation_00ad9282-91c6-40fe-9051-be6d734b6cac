"""
SQLite database models for the Advanced RAG Chatbot.
"""

import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Optional

from ..utils.logger import get_logger
from ..config import CONVERSATION_DB_PATH, DOCUMENT_DB_PATH, USER_DB_PATH

logger = get_logger(__name__)


class BaseDatabase:
    def __init__(self, db_path: Path):
        self.db_path = db_path
        self._ensure_tables()

    def _get_connection(self) -> sqlite3.Connection:
        conn = sqlite3.connect(self.db_path, isolation_level=None)
        conn.row_factory = sqlite3.Row
        return conn

    def _ensure_tables(self):
        raise NotImplementedError("Subclasses must implement table creation logic.")


class ConversationDatabase(BaseDatabase):
    def _ensure_tables(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT NOT NULL,
                    user_query TEXT NOT NULL,
                    assistant_response TEXT NOT NULL,
                    language TEXT NOT NULL,
                    query_timestamp TIMESTAMP NOT NULL,
                    response_timestamp TIMESTAMP NOT NULL,
                    response_time_seconds REAL NOT NULL
                )
            ''')
            conn.commit()
            logger.info("✅ Conversations table initialized.")


class DocumentDatabase(BaseDatabase):
    def _ensure_tables(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    source_file TEXT NOT NULL,
                    chunk_index INTEGER NOT NULL,
                    embedding_file TEXT,
                    created_at TIMESTAMP NOT NULL
                )
            ''')
            conn.commit()
            logger.info("✅ Documents table initialized.")


class UserDatabase(BaseDatabase):
    def _ensure_tables(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL UNIQUE,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    employee_id TEXT UNIQUE,
                    two_fa_secret TEXT,
                    role TEXT DEFAULT 'user',
                    created_at TIMESTAMP NOT NULL,
                    last_login TIMESTAMP
                )
            ''')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
            conn.commit()
            logger.info("✅ Users table initialized.")


class ConversationModel:
    def __init__(self):
        self.db = ConversationDatabase(CONVERSATION_DB_PATH)

    def save_conversation(self, device_id, user_query, assistant_response, language, query_timestamp, response_timestamp):
        try:
            response_time = round(response_timestamp - query_timestamp, 3)
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO conversations (device_id, user_query, assistant_response, language, query_timestamp, response_timestamp, response_time_seconds)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    device_id,
                    user_query,
                    assistant_response,
                    language,
                    datetime.fromtimestamp(query_timestamp).isoformat(),
                    datetime.fromtimestamp(response_timestamp).isoformat(),
                    response_time
                ))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.exception("❌ Failed to save conversation")
            return None


class DocumentModel:
    def __init__(self):
        self.db = DocumentDatabase(DOCUMENT_DB_PATH)

    def save_document(self, title, content, source_file, chunk_index, embedding_file=None):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO documents (title, content, source_file, chunk_index, embedding_file, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    title,
                    content,
                    source_file,
                    chunk_index,
                    embedding_file,
                    datetime.now().isoformat()
                ))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.exception("❌ Failed to save document")
            return None


class UserModel:
    def __init__(self):
        self.db = UserDatabase(USER_DB_PATH)

    def create_user(self, email, password_hash, full_name, employee_id=None, two_fa_secret=None, role='user'):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO users (email, password_hash, full_name, employee_id, two_fa_secret, role, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    email,
                    password_hash,
                    full_name,
                    employee_id,
                    two_fa_secret,
                    role,
                    datetime.now().isoformat()
                ))
                conn.commit()
                return cursor.lastrowid
        except sqlite3.IntegrityError:
            logger.warning(f"⚠️ User with email {email} already exists.")
            return None
        except Exception as e:
            logger.exception("❌ Failed to create user")
            return None

    def get_user_by_email(self, email):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM users WHERE email = ?', (email,))
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logger.exception("❌ Failed to get user by email")
            return None

    def get_user_by_id(self, user_id):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logger.exception("❌ Failed to get user by ID")
            return None

    def update_last_login(self, user_id):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE users SET last_login = ? WHERE id = ?
                ''', (
                    datetime.now().isoformat(),
                    user_id
                ))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.exception("❌ Failed to update last login")
            return False
