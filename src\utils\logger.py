"""
Advanced Production-Grade Logging System

A comprehensive, thread-safe, and highly configurable logging system designed for
production environments with proper resource management, health monitoring,
and graceful degradation capabilities.

Features:
- Thread-safe singleton pattern with proper lifecycle management
- Comprehensive health monitoring and metrics
- Dynamic log level changes via signals
- Circuit breaker pattern for external destinations
- Structured error codes and comprehensive error handling
- Multiple output formats (text, JSON, structured)
- Time and size-based log rotation
- Graceful shutdown with resource cleanup
- Configuration validation and fallbacks
- Performance monitoring and queue health checks
"""

import os
import sys
import json
import time
import signal
import atexit
import threading
import traceback
import datetime
from enum import Enum
from pathlib import Path
from queue import Queue, Empty, Full
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union, Any, Callable
from contextlib import contextmanager
import logging
import logging.handlers
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
# Add import for concurrent-log-handler
try:
    from concurrent_log_handler import ConcurrentRotatingFileHandler
    _has_concurrent_handler = True
except ImportError:
    _has_concurrent_handler = False


# =============================================================================
# CONFIGURATION AND ENUMS
# =============================================================================

class LogLevel(Enum):
    """Standardized log levels with numeric values."""
    CRITICAL = 50
    ERROR = 40
    WARNING = 30
    INFO = 20
    DEBUG = 10
    NOTSET = 0

class LogFormat(Enum):
    """Available log output formats."""
    TEXT = "text"
    JSON = "json"
    STRUCTURED = "structured"

class RotationType(Enum):
    """Log rotation strategies."""
    SIZE = "size"
    TIME = "time"
    BOTH = "both"

class ErrorCode(Enum):
    """Structured error codes for better observability."""
    CONFIG_INVALID = "LOG_E001"
    QUEUE_FULL = "LOG_E002"
    HANDLER_FAILED = "LOG_E003"
    LISTENER_FAILED = "LOG_E004"
    SHUTDOWN_TIMEOUT = "LOG_E005"
    HEALTH_CHECK_FAILED = "LOG_E006"
    CIRCUIT_BREAKER_OPEN = "LOG_E007"


@dataclass
class LoggingConfig:
    """Comprehensive logging configuration with validation."""
    
    # Directory and file settings
    log_dir: Path = field(default_factory=lambda: Path("logs"))
    log_filename: str = "application.log"
    
    # File rotation settings
    rotation_type: RotationType = RotationType.BOTH
    max_bytes: int = 50 * 1024 * 1024  # 50MB
    backup_count: int = 10
    when: str = "midnight"  # For time-based rotation
    interval: int = 1
    
    # Log levels
    default_level: LogLevel = LogLevel.INFO
    console_level: LogLevel = LogLevel.INFO
    file_level: LogLevel = LogLevel.DEBUG
    
    # Output formats
    console_format: LogFormat = LogFormat.TEXT
    file_format: LogFormat = LogFormat.JSON
    
    # Async settings
    queue_size: int = 10000
    queue_timeout: float = 1.0
    listener_timeout: float = 5.0
    
    # Health monitoring
    health_check_interval: float = 60.0  # seconds
    max_queue_size_percent: float = 0.8  # Alert when queue is 80% full
    
    # Security
    sensitive_fields: List[str] = field(default_factory=lambda: [
        'password', 'token', 'secret', 'key', 'auth', 'credential'
    ])
    
    # Performance
    enable_metrics: bool = True
    metrics_interval: float = 300.0  # 5 minutes
    
    # Circuit breaker settings
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        self._validate()
    
    def _validate(self):
        """Comprehensive configuration validation."""
        errors = []
        
        # Validate log directory
        try:
            self.log_dir = Path(self.log_dir)
            if not self.log_dir.exists():
                self.log_dir.mkdir(parents=True, exist_ok=True)
            if not os.access(self.log_dir, os.W_OK):
                errors.append(f"Log directory not writable: {self.log_dir}")
        except Exception as e:
            errors.append(f"Invalid log directory: {e}")
        
        # Validate numeric settings
        if self.max_bytes <= 0:
            errors.append("max_bytes must be positive")
        if self.backup_count < 0:
            errors.append("backup_count cannot be negative")
        if self.queue_size <= 0:
            errors.append("queue_size must be positive")
        if not 0 < self.max_queue_size_percent <= 1:
            errors.append("max_queue_size_percent must be between 0 and 1")
        
        # Validate timeouts
        if self.queue_timeout <= 0:
            errors.append("queue_timeout must be positive")
        if self.listener_timeout <= 0:
            errors.append("listener_timeout must be positive")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
    
    @classmethod
    def from_env(cls) -> 'LoggingConfig':
        """Create configuration from environment variables."""
        config = cls()
        
        # Override with environment variables
        if log_dir := os.getenv("LOG_DIR"):
            config.log_dir = Path(log_dir)
        if log_filename := os.getenv("LOG_FILENAME"):
            config.log_filename = log_filename
        if max_bytes := os.getenv("LOG_MAX_BYTES"):
            config.max_bytes = int(max_bytes)
        if backup_count := os.getenv("LOG_BACKUP_COUNT"):
            config.backup_count = int(backup_count)
        if default_level := os.getenv("LOG_LEVEL"):
            config.default_level = LogLevel[default_level.upper()]
        if console_level := os.getenv("CONSOLE_LOG_LEVEL"):
            config.console_level = LogLevel[console_level.upper()]
        if file_level := os.getenv("FILE_LOG_LEVEL"):
            config.file_level = LogLevel[file_level.upper()]
        if queue_size := os.getenv("LOG_QUEUE_SIZE"):
            config.queue_size = int(queue_size)
        
        return config


# =============================================================================
# FORMATTERS
# =============================================================================

class BaseFormatter(logging.Formatter):
    """Base formatter with common functionality."""
    
    def __init__(self, config: LoggingConfig):
        super().__init__()
        self.config = config
        self.sensitive_fields = set(field.lower() for field in config.sensitive_fields)
    
    def _sanitize_record(self, record: logging.LogRecord) -> Dict[str, Any]:
        """Remove or mask sensitive information from log record."""
        sanitized = {}
        
        for key, value in record.__dict__.items():
            if key.lower() in self.sensitive_fields:
                sanitized[key] = "***REDACTED***"
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_dict(value)
            else:
                sanitized[key] = value
        
        return sanitized
    
    def _sanitize_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively sanitize dictionary data."""
        sanitized = {}
        for key, value in data.items():
            if key.lower() in self.sensitive_fields:
                sanitized[key] = "***REDACTED***"
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_dict(value)
            else:
                sanitized[key] = value
        return sanitized


class StructuredTextFormatter(BaseFormatter):
    """Enhanced text formatter with structured output."""
    
    def __init__(self, config: LoggingConfig, include_extra: bool = True):
        super().__init__(config)
        self.include_extra = include_extra
        self.base_format = (
            "%(asctime)s | %(name)s:%(lineno)d | %(levelname)-8s | "
            "%(funcName)s | PID:%(process)d | TID:%(threadName)s | %(message)s"
        )
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with enhanced structure."""
        # Apply base formatting
        formatted = logging.Formatter(self.base_format).format(record)
        
        # Add extra fields if enabled
        if self.include_extra:
            extra_fields = []
            sanitized = self._sanitize_record(record)
            
            for key, value in sanitized.items():
                if key not in self._get_standard_fields() and not key.startswith('_'):
                    extra_fields.append(f"{key}={value}")
            
            if extra_fields:
                formatted += f" | EXTRA: {' | '.join(extra_fields)}"
        
        # Add exception information
        if record.exc_info:
            formatted += f"\n--- EXCEPTION START ---\n{self.formatException(record.exc_info)}\n--- EXCEPTION END ---"
        
        return formatted
    
    def _get_standard_fields(self) -> set:
        """Get standard log record fields to exclude from extra."""
        return {
            'name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 'filename',
            'module', 'lineno', 'funcName', 'created', 'msecs', 'relativeCreated',
            'thread', 'threadName', 'processName', 'process', 'message', 'exc_info',
            'exc_text', 'stack_info', 'asctime'
        }


class ProductionJSONFormatter(BaseFormatter):
    """Production-ready JSON formatter with comprehensive metadata."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        sanitized = self._sanitize_record(record)
        
        log_entry = {
            "@timestamp": datetime.datetime.fromtimestamp(record.created).isoformat(),
            "@version": "1",
            "host": os.uname().nodename if hasattr(os, 'uname') else 'unknown',
            "logger": {
                "name": record.name,
                "level": record.levelname,
                "level_value": record.levelno
            },
            "message": record.getMessage(),
            "source": {
                "file": record.filename,
                "line": record.lineno,
                "function": record.funcName,
                "module": record.module,
                "path": record.pathname
            },
            "process": {
                "pid": record.process,
                "name": getattr(record, 'processName', 'MainProcess')
            },
            "thread": {
                "id": record.thread,
                "name": record.threadName
            },
            "timing": {
                "created": record.created,
                "msecs": record.msecs,
                "relative_created": record.relativeCreated
            }
        }
        
        # Add custom fields
        for key, value in sanitized.items():
            if key not in self._get_standard_fields() and not key.startswith('_'):
                log_entry.setdefault("custom", {})[key] = value
        
        # Add exception information
        if record.exc_info:
            log_entry["exception"] = {
                "class": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info)
            }
        
        if record.stack_info:
            log_entry["stack_trace"] = self.formatStack(record.stack_info)
        
        return json.dumps(log_entry, ensure_ascii=False, default=str)
    
    def _get_standard_fields(self) -> set:
        """Get standard log record fields."""
        return {
            'name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 'filename',
            'module', 'lineno', 'funcName', 'created', 'msecs', 'relativeCreated',
            'thread', 'threadName', 'processName', 'process', 'message', 'exc_info',
            'exc_text', 'stack_info', 'asctime'
        }


# =============================================================================
# CIRCUIT BREAKER
# =============================================================================

class CircuitBreaker:
    """Circuit breaker pattern for logging handlers."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self._lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection."""
        with self._lock:
            if self.state == "OPEN":
                if time.time() - self.last_failure_time > self.recovery_timeout:
                    self.state = "HALF_OPEN"
                else:
                    raise Exception(f"Circuit breaker OPEN: {ErrorCode.CIRCUIT_BREAKER_OPEN.value}")
            
            try:
                result = func(*args, **kwargs)
                if self.state == "HALF_OPEN":
                    self.state = "CLOSED"
                    self.failure_count = 0
                return result
            except Exception as e:
                self.failure_count += 1
                self.last_failure_time = time.time()
                
                if self.failure_count >= self.failure_threshold:
                    self.state = "OPEN"
                
                raise e


# =============================================================================
# HEALTH MONITORING
# =============================================================================

@dataclass
class HealthMetrics:
    """Health and performance metrics."""
    queue_size: int = 0
    queue_max_size: int = 0
    messages_processed: int = 0
    messages_dropped: int = 0
    errors_count: int = 0
    last_error: Optional[str] = None
    uptime_seconds: float = 0
    circuit_breaker_state: str = "CLOSED"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "queue_size": self.queue_size,
            "queue_utilization": self.queue_size / max(self.queue_max_size, 1),
            "messages_processed": self.messages_processed,
            "messages_dropped": self.messages_dropped,
            "error_rate": self.errors_count / max(self.messages_processed, 1),
            "errors_count": self.errors_count,
            "last_error": self.last_error,
            "uptime_seconds": self.uptime_seconds,
            "circuit_breaker_state": self.circuit_breaker_state
        }


class HealthMonitor:
    """Health monitoring and metrics collection."""
    
    def __init__(self, config: LoggingConfig):
        self.config = config
        self.metrics = HealthMetrics()
        self.start_time = time.time()
        self._lock = threading.Lock()
        self._monitoring_thread = None
        self._stop_event = threading.Event()
        
    def start(self):
        """Start health monitoring thread."""
        if self._monitoring_thread is None or not self._monitoring_thread.is_alive():
            self._monitoring_thread = threading.Thread(target=self._monitor_health)
            self._monitoring_thread.daemon = True
            self._monitoring_thread.start()
    
    def stop(self):
        """Stop health monitoring."""
        self._stop_event.set()
        if self._monitoring_thread:
            self._monitoring_thread.join(timeout=5)
    
    def update_metrics(self, **kwargs):
        """Thread-safe metrics update."""
        with self._lock:
            for key, value in kwargs.items():
                if hasattr(self.metrics, key):
                    setattr(self.metrics, key, value)
            self.metrics.uptime_seconds = time.time() - self.start_time
    
    def increment_counter(self, counter_name: str):
        """Increment a counter metric."""
        with self._lock:
            current_value = getattr(self.metrics, counter_name, 0)
            setattr(self.metrics, counter_name, current_value + 1)
    
    def record_error(self, error: str):
        """Record an error occurrence."""
        with self._lock:
            self.metrics.errors_count += 1
            self.metrics.last_error = error
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status."""
        with self._lock:
            health_data = self.metrics.to_dict()
            
            # Determine overall health
            is_healthy = (
                health_data["queue_utilization"] < self.config.max_queue_size_percent and
                health_data["circuit_breaker_state"] != "OPEN" and
                health_data["error_rate"] < 0.05  # Less than 5% error rate
            )
            
            health_data.update({
                "status": "HEALTHY" if is_healthy else "UNHEALTHY",
                "timestamp": datetime.datetime.now().isoformat()
            })
            
            return health_data
    
    def _monitor_health(self):
        """Internal health monitoring loop."""
        while not self._stop_event.wait(self.config.health_check_interval):
            try:
                health_status = self.get_health_status()
                
                # Log health status if configured
                if self.config.enable_metrics:
                    # Use a separate logger to avoid circular logging
                    print(f"[HEALTH] {json.dumps(health_status)}")
                
                # Alert on unhealthy status
                if health_status["status"] == "UNHEALTHY":
                    print(f"[ALERT] Logging system unhealthy: {health_status}")
                    
            except Exception as e:
                print(f"[ERROR] Health monitoring failed: {e}")


# =============================================================================
# MAIN LOGGING MANAGER
# =============================================================================

class ProductionLogManager:
    """
    Thread-safe, production-ready logging manager with comprehensive features.
    
    This is a singleton class that manages the entire logging lifecycle with
    proper resource management, health monitoring, and graceful degradation.
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, config: Optional[LoggingConfig] = None):
        """Singleton pattern implementation."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config: Optional[LoggingConfig] = None):
        """Initialize the logging manager."""
        if hasattr(self, '_initialized'):
            return
        
        self.config = config or LoggingConfig.from_env()
        self.log_queue = Queue(maxsize=self.config.queue_size)
        self.health_monitor = HealthMonitor(self.config)
        self.circuit_breaker = CircuitBreaker(
            self.config.failure_threshold, 
            self.config.recovery_timeout
        )
        
        # Internal state
        self._listener = None
        self._listener_thread = None
        self._loggers = {}
        self._shutdown_initiated = False
        self._initialization_lock = threading.Lock()
        
        # Initialize components
        self._setup_handlers()
        self._setup_signal_handlers()
        self._start_services()
        
        # Register cleanup
        atexit.register(self.shutdown)
        
        self._initialized = True
    
    def _setup_handlers(self):
        """Setup file and console handlers."""
        try:
            # File handler
            log_file = self.config.log_dir / self.config.log_filename
            
            if _has_concurrent_handler:
                # ConcurrentRotatingFileHandler only supports size-based rotation
                self.file_handler = ConcurrentRotatingFileHandler(
                    log_file,
                    maxBytes=self.config.max_bytes,
                    backupCount=self.config.backup_count,
                    encoding='utf-8'
                )
            else:
                if self.config.rotation_type == RotationType.SIZE:
                    self.file_handler = RotatingFileHandler(
                        log_file,
                        maxBytes=self.config.max_bytes,
                        backupCount=self.config.backup_count,
                        encoding='utf-8'
                    )
                elif self.config.rotation_type == RotationType.TIME:
                    self.file_handler = TimedRotatingFileHandler(
                        log_file,
                        when=self.config.when,
                        interval=self.config.interval,
                        backupCount=self.config.backup_count,
                        encoding='utf-8'
                    )
                else:  # BOTH - use time-based with size limit
                    self.file_handler = TimedRotatingFileHandler(
                        log_file,
                        when=self.config.when,
                        interval=self.config.interval,
                        backupCount=self.config.backup_count,
                        encoding='utf-8'
                    )
            
            # Set file formatter
            if self.config.file_format == LogFormat.JSON:
                file_formatter = ProductionJSONFormatter(self.config)
            else:
                file_formatter = StructuredTextFormatter(self.config)
            
            self.file_handler.setFormatter(file_formatter)
            self.file_handler.setLevel(self.config.file_level.value)
            
            # Console handler
            self.console_handler = logging.StreamHandler(sys.stdout)
            
            # Set console formatter
            if self.config.console_format == LogFormat.JSON:
                console_formatter = ProductionJSONFormatter(self.config)
            else:
                console_formatter = StructuredTextFormatter(self.config, include_extra=False)
            
            self.console_handler.setFormatter(console_formatter)
            self.console_handler.setLevel(self.config.console_level.value)
            
            # Configure UTF-8 encoding for console
            if hasattr(self.console_handler.stream, 'reconfigure'):
                try:
                    self.console_handler.stream.reconfigure(encoding='utf-8')
                except Exception:
                    pass  # Fallback gracefully
                    
        except Exception as e:
            raise RuntimeError(f"Failed to setup handlers: {e}")
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown and dynamic reconfiguration."""
        def signal_handler(signum, frame):
            if signum == signal.SIGTERM or signum == signal.SIGINT:
                print("Received shutdown signal, initiating graceful shutdown...")
                self.shutdown()
            elif signum == signal.SIGUSR1:  # Dynamic log level change
                self._cycle_log_levels()
        
        try:
            signal.signal(signal.SIGTERM, signal_handler)
            signal.signal(signal.SIGINT, signal_handler)
            if hasattr(signal, 'SIGUSR1'):  # Unix only
                signal.signal(signal.SIGUSR1, signal_handler)
        except Exception as e:
            print(f"Warning: Could not setup signal handlers: {e}")
    
    def _cycle_log_levels(self):
        """Cycle through log levels for debugging."""
        levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARNING, LogLevel.ERROR]
        current_idx = levels.index(self.config.default_level)
        next_level = levels[(current_idx + 1) % len(levels)]
        
        print(f"Changing log level from {self.config.default_level.name} to {next_level.name}")
        self.config.default_level = next_level
        
        # Update existing loggers
        for logger in self._loggers.values():
            logger.setLevel(next_level.value)
    
    def _start_services(self):
        """Start background services."""
        try:
            # Start queue listener
            self._listener = logging.handlers.QueueListener(
                self.log_queue, 
                self.file_handler, 
                self.console_handler,
                respect_handler_level=True
            )
            self._listener_thread = threading.Thread(target=self._listener.start)
            self._listener_thread.daemon = True
            self._listener_thread.start()
            
            # Start health monitoring
            self.health_monitor.start()
            
            # Update metrics
            self.health_monitor.update_metrics(
                queue_max_size=self.config.queue_size,
                circuit_breaker_state=self.circuit_breaker.state
            )
            
        except Exception as e:
            raise RuntimeError(f"Failed to start services: {e}")
    
    def get_logger(self, name: str, extra_context: Optional[Dict[str, Any]] = None) -> logging.Logger:
        """
        Get or create a logger with the given name.
        
        Args:
            name: Logger name (typically __name__)
            extra_context: Additional context to include in all log messages
            
        Returns:
            Configured logger instance
        """
        with self._initialization_lock:
            if name in self._loggers:
                return self._loggers[name]
            
            logger = logging.getLogger(name)
            logger.setLevel(self.config.default_level.value)
            logger.propagate = False
            
            # Clear any existing handlers
            logger.handlers.clear()
            
            # Add queue handler
            queue_handler = logging.handlers.QueueHandler(self.log_queue)
            
            # Add context if provided
            if extra_context:
                class ContextFilter(logging.Filter):
                    def filter(self, record):
                        for key, value in extra_context.items():
                            setattr(record, key, value)
                        return True
                
                queue_handler.addFilter(ContextFilter())
            
            logger.addHandler(queue_handler)
            self._loggers[name] = logger
            
            return logger
    
    def log_with_context(self, logger_name: str, level: str, message: str, **context):
        """
        Enhanced logging with structured context.
        
        Args:
            logger_name: Name of the logger
            level: Log level (debug, info, warning, error, critical)
            message: Log message
            **context: Additional context fields
        """
        try:
            logger = self.get_logger(logger_name)
            log_method = getattr(logger, level.lower())
            
            # Add performance timing
            context['log_timestamp'] = time.time()
            
            # Attempt to log with circuit breaker protection
            self.circuit_breaker.call(log_method, message, extra=context)
            
            # Update metrics
            self.health_monitor.increment_counter('messages_processed')
            self.health_monitor.update_metrics(
                queue_size=self.log_queue.qsize(),
                circuit_breaker_state=self.circuit_breaker.state
            )
            
        except Full:
            # Queue is full - increment dropped messages
            self.health_monitor.increment_counter('messages_dropped')
            self.health_monitor.record_error(f"Queue full: {ErrorCode.QUEUE_FULL.value}")
            
            # Fallback to direct console output
            print(f"[DROPPED] {datetime.datetime.now().isoformat()} - {logger_name} - {level.upper()} - {message}")
            
        except Exception as e:
            error_msg = f"Logging failed: {e}"
            self.health_monitor.record_error(error_msg)
            
            # Fallback logging
            print(f"[ERROR] {datetime.datetime.now().isoformat()} - LOGGING_SYSTEM - ERROR - {error_msg}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status of the logging system."""
        return self.health_monitor.get_health_status()
    
    @contextmanager
    def performance_context(self, operation_name: str, logger_name: str = "performance"):
        """Context manager for performance monitoring."""
        start_time = time.time()
        logger = self.get_logger(logger_name)
        
        try:
            yield
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                f"Operation failed: {operation_name}",
                extra={
                    'operation': operation_name,
                    'duration_seconds': duration,
                    'status': 'failed',
                    'error': str(e)
                }
            )
            raise
        else:
            duration = time.time() - start_time
            logger.info(
                f"Operation completed: {operation_name}",
                extra={
                    'operation': operation_name,
                    'duration_seconds': duration,
                    'status': 'success'
                }
            )
    
    def shutdown(self, timeout: float = None):
        """
        Graceful shutdown of the logging system.
        
        Args:
            timeout: Maximum time to wait for shutdown (uses config default if None)
        """
        if self._shutdown_initiated:
            return
        
        self._shutdown_initiated = True
        timeout = timeout or self.config.listener_timeout
        
        try:
            print("Shutting down logging system...")
            
            # Stop health monitoring
            self.health_monitor.stop()
            
            # Flush remaining log messages
            remaining_messages = self.log_queue.qsize()
            if remaining_messages > 0:
                print(f"Flushing {remaining_messages} remaining log messages...")
                
                # Give some time for queue to drain
                start_time = time.time()
                while (self.log_queue.qsize() > 0 and 
                       time.time() - start_time < timeout):
                    time.sleep(0.1)
            
            # Stop listener
            if self._listener:
                self._listener.stop()
                
            # Wait for listener thread
            if self._listener_thread and self._listener_thread.is_alive():
                self._listener_thread.join(timeout=timeout)
                
                if self._listener_thread.is_alive():
                    print(f"Warning: Listener thread did not stop within {timeout}s")
                    self.health_monitor.record_error(f"Shutdown timeout: {ErrorCode.SHUTDOWN_TIMEOUT.value}")
            
            # Close handlers
            if hasattr(self, 'file_handler'):
                self.file_handler.close()
            if hasattr(self, 'console_handler'):
                self.console_handler.close()
            
            print("Logging system shutdown complete.")
            
        except Exception as e:
            print(f"Error during logging system shutdown: {e}")
    
    def __del__(self):
        """Cleanup on destruction."""
        if not self._shutdown_initiated:
            self.shutdown()


# =============================================================================
# CONVENIENCE FUNCTIONS
# =============================================================================

# Global manager instance
_global_manager: Optional[ProductionLogManager] = None

def initialize_logging(config: Optional[LoggingConfig] = None) -> ProductionLogManager:
    """
    Initialize the global logging system.
    
    Args:
        config: Optional logging configuration (uses environment defaults if None)
        
    Returns:
        ProductionLogManager instance
    """
    global _global_manager
    _global_manager = ProductionLogManager(config)
    return _global_manager

def get_logger(name: str, extra_context: Optional[Dict[str, Any]] = None) -> logging.Logger:
    """
    Get a logger from the global logging manager.
    
    Args:
        name: Logger name (typically __name__)
        extra_context: Additional context for all log messages
        
    Returns:
        Configured logger instance
        
    Raises:
        RuntimeError: If logging system not initialized
    """
    if _global_manager is None:
        # Auto-initialize with default config
        initialize_logging()
    
    return _global_manager.get_logger(name, extra_context)

def shutdown_logging(timeout: float = None):
    """
    Shutdown the global logging system.
    
    Args:
        timeout: Maximum time to wait for shutdown
    """
    global _global_manager
    if _global_manager:
        _global_manager.shutdown(timeout)
        _global_manager = None

def get_health_status() -> Dict[str, Any]:
    """Get health status of the logging system."""
    if _global_manager is None:
        return {"status": "NOT_INITIALIZED", "error": "Logging system not initialized"}
    
    return _global_manager.get_health_status()

def log_with_context(logger_name: str, level: str, message: str, **context):
    """
    Enhanced logging with structured context.
    
    Args:
        logger_name: Name of the logger
        level: Log level (debug, info, warning, error, critical)
        message: Log message
        **context: Additional context fields
    """
    if _global_manager is None:
        initialize_logging()
    
    _global_manager.log_with_context(logger_name, level, message, **context)

@contextmanager
def performance_context(operation_name: str, logger_name: str = "performance"):
    """
    Context manager for performance monitoring.
    
    Args:
        operation_name: Name of the operation being measured
        logger_name: Logger to use for performance logs
    """
    if _global_manager is None:
        initialize_logging()
    
    with _global_manager.performance_context(operation_name, logger_name):
        yield


# =============================================================================
# EXAMPLE USAGE AND TESTING
# =============================================================================

def run_comprehensive_tests():
    """Comprehensive test suite for the logging system."""
    print("=" * 80)
    print("PRODUCTION LOGGING SYSTEM - COMPREHENSIVE TESTS")
    print("=" * 80)
    
    try:
        # Test 1: Basic Configuration
        print("\n1. Testing Configuration...")
        config = LoggingConfig.from_env()
        config.log_dir = Path("test_logs")
        config.enable_metrics = True
        config.health_check_interval = 5.0  # Faster for testing
        
        # Test 2: Initialize Logging System
        print("\n2. Initializing Logging System...")
        manager = initialize_logging(config)
        
        # Test 3: Basic Logging
        print("\n3. Testing Basic Logging...")
        logger = get_logger("test.basic")
        logger.debug("Debug message - should appear in file only")
        logger.info("Info message - basic functionality test")
        logger.warning("Warning message - something might be wrong")
        logger.error("Error message - something went wrong")
        logger.critical("Critical message - system in danger")
        
        # Test 4: Structured Logging with Context
        print("\n4. Testing Structured Logging...")
        log_with_context(
            "test.structured", 
            "info", 
            "User login attempt",
            user_id="12345",
            ip_address="*************",
            user_agent="Mozilla/5.0...",
            session_id="abc-def-123",
            timestamp=datetime.datetime.now().isoformat()
        )
        
        # Test 5: Exception Logging
        print("\n5. Testing Exception Logging...")
        exception_logger = get_logger("test.exceptions")
        try:
            result = 10 / 0
        except ZeroDivisionError as e:
            exception_logger.error(
                "Division by zero error occurred",
                exc_info=True,
                extra={
                    "operation": "division",
                    "dividend": 10,
                    "divisor": 0,
                    "error_code": ErrorCode.CONFIG_INVALID.value
                }
            )
        
        # Test 6: Performance Context
        print("\n6. Testing Performance Context...")
        with performance_context("database_query", "test.performance"):
            time.sleep(0.1)  # Simulate work
            # Simulate database operations
            log_with_context(
                "test.performance",
                "debug",
                "Executing database query",
                query="SELECT * FROM users WHERE active = 1",
                params={"active": 1}
            )
        
        # Test 7: High Volume Logging
        print("\n7. Testing High Volume Logging...")
        volume_logger = get_logger("test.volume")
        
        def high_volume_test():
            for i in range(100):
                volume_logger.info(
                    f"High volume message {i}",
                    extra={
                        "batch_id": "batch_001",
                        "message_number": i,
                        "thread_id": threading.current_thread().ident
                    }
                )
        
        # Run high volume test in multiple threads
        threads = []
        for t in range(3):
            thread = threading.Thread(target=high_volume_test)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # Test 8: Security Testing (Sensitive Data Filtering)
        print("\n8. Testing Security Features...")
        security_logger = get_logger("test.security")
        security_logger.warning(
            "Authentication attempt",
            extra={
                "username": "john_doe",
                "password": "super_secret_password",  # Should be filtered
                "token": "jwt_token_12345",  # Should be filtered
                "ip_address": "************",
                "user_agent": "curl/7.68.0"
            }
        )
        
        # Test 9: Health Status
        print("\n9. Testing Health Monitoring...")
        health_status = get_health_status()
        print(f"Health Status: {json.dumps(health_status, indent=2)}")
        
        # Test 10: Dynamic Log Level Change (Unix only)
        print("\n10. Testing Dynamic Configuration...")
        if hasattr(os, 'kill') and hasattr(signal, 'SIGUSR1'):
            try:
                # Send signal to self to change log level
                os.kill(os.getpid(), signal.SIGUSR1)
                time.sleep(0.5)  # Allow signal processing
                
                # Log at different levels to see the change
                test_logger = get_logger("test.dynamic")
                test_logger.debug("Debug after level change")
                test_logger.info("Info after level change")
                test_logger.warning("Warning after level change")
            except Exception as e:
                print(f"Dynamic level change test failed (expected on Windows): {e}")
        
        # Test 11: Edge Cases and Error Handling
        print("\n11. Testing Edge Cases...")
        
        # Test with None values
        edge_logger = get_logger("test.edge_cases")
        edge_logger.info(
            "Testing edge cases",
            extra={
                "none_value": None,
                "empty_string": "",
                "large_number": 123456789012345,
                "unicode_text": "Testing 中文 and émojis 🚀",
                "nested_dict": {
                    "level1": {
                        "level2": {
                            "secret": "should_be_filtered",
                            "data": [1, 2, 3, 4, 5]
                        }
                    }
                }
            }
        )
        
        # Test 12: Context Manager Error Handling
        print("\n12. Testing Context Manager Error Handling...")
        try:
            with performance_context("failing_operation", "test.error_context"):
                raise ValueError("Simulated operation failure")
        except ValueError:
            pass  # Expected
        
        # Test 13: Final Health Check
        print("\n13. Final Health Check...")
        time.sleep(2)  # Allow metrics to update
        final_health = get_health_status()
        print(f"Final Health Status: {json.dumps(final_health, indent=2)}")
        
        print("\n" + "=" * 80)
        print("ALL TESTS COMPLETED SUCCESSFULLY!")
        print("Check the 'test_logs/application.log' file for detailed output.")
        print("=" * 80)
        
    except Exception as e:
        print(f"\nTEST FAILED: {e}")
        traceback.print_exc()
    
    finally:
        # Always attempt cleanup
        print("\nInitiating graceful shutdown...")
        shutdown_logging(timeout=10.0)
        print("Test suite completed.")

def run_stress_test(duration_seconds: int = 30, threads: int = 5, messages_per_second: int = 100):
    """
    Run a stress test of the logging system.
    
    Args:
        duration_seconds: How long to run the test
        threads: Number of concurrent threads
        messages_per_second: Target messages per second per thread
    """
    print(f"\nSTRESS TEST: {threads} threads, {messages_per_second} msg/sec/thread, {duration_seconds}s duration")
    
    # Initialize with high-performance config
    config = LoggingConfig()
    config.queue_size = 50000  # Large queue
    config.health_check_interval = 10.0
    config.enable_metrics = True
    
    manager = initialize_logging(config)
    
    stop_event = threading.Event()
    results = {"messages_sent": 0, "errors": 0}
    results_lock = threading.Lock()
    
    def stress_worker(worker_id: int):
        """Stress test worker function."""
        logger = get_logger(f"stress.worker_{worker_id}")
        local_count = 0
        local_errors = 0
        
        message_interval = 1.0 / messages_per_second
        
        while not stop_event.is_set():
            try:
                start_time = time.time()
                
                logger.info(
                    f"Stress test message from worker {worker_id}",
                    extra={
                        "worker_id": worker_id,
                        "message_count": local_count,
                        "timestamp": time.time(),
                        "thread_name": threading.current_thread().name,
                        "data_payload": f"payload_{local_count}_{worker_id}"
                    }
                )
                
                local_count += 1
                
                # Maintain target rate
                elapsed = time.time() - start_time
                sleep_time = message_interval - elapsed
                if sleep_time > 0:
                    time.sleep(sleep_time)
                    
            except Exception as e:
                local_errors += 1
                print(f"Worker {worker_id} error: {e}")
        
        # Update global results
        with results_lock:
            results["messages_sent"] += local_count
            results["errors"] += local_errors
    
    # Start worker threads
    workers = []
    for i in range(threads):
        worker = threading.Thread(target=stress_worker, args=(i,))
        workers.append(worker)
        worker.start()
    
    # Run for specified duration
    time.sleep(duration_seconds)
    stop_event.set()
    
    # Wait for workers to finish
    for worker in workers:
        worker.join(timeout=5)
    
    # Get final metrics
    final_health = get_health_status()
    
    print(f"\nSTRESS TEST RESULTS:")
    print(f"Messages Sent: {results['messages_sent']}")
    print(f"Errors: {results['errors']}")
    print(f"Success Rate: {(1 - results['errors'] / max(results['messages_sent'], 1)) * 100:.2f}%")
    print(f"Messages/Second: {results['messages_sent'] / duration_seconds:.2f}")
    print(f"Final Health: {final_health['status']}")
    print(f"Queue Utilization: {final_health['queue_utilization']:.2%}")
    print(f"Messages Processed: {final_health['messages_processed']}")
    print(f"Messages Dropped: {final_health['messages_dropped']}")
    
    # Cleanup
    shutdown_logging()


# =============================================================================
# MAIN ENTRY POINT
# =============================================================================

if __name__ == "__main__":
    """
    Main entry point for testing and demonstration.
    
    Usage:
        python logging_system.py                    # Run comprehensive tests
        python logging_system.py --stress          # Run stress test
        python logging_system.py --stress --duration 60 --threads 10  # Custom stress test
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="Production Logging System Test Suite")
    parser.add_argument("--stress", action="store_true", help="Run stress test instead of comprehensive tests")
    parser.add_argument("--duration", type=int, default=30, help="Stress test duration in seconds")
    parser.add_argument("--threads", type=int, default=5, help="Number of stress test threads")
    parser.add_argument("--rate", type=int, default=100, help="Messages per second per thread")
    
    args = parser.parse_args()
    
    if args.stress:
        run_stress_test(args.duration, args.threads, args.rate)
    else:
        run_comprehensive_tests()