/* Message Files Styles - ChatGPT Style */
/* Apply consistent font family */
.message-files,
.file-items,
.message-file-item,
.message-file-item .file-name,
.message-file-item .file-type {
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}
.message-files {
    margin-top: 5px;
    margin-bottom: 5px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.file-items {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    overflow-x: auto;
    padding: 4px 0;
    scrollbar-width: none; /* Hide scrollbar for Firefox */
    -ms-overflow-style: none; /* Hide scrollbar for IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.file-items::-webkit-scrollbar {
    display: none;
}

.message-file-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    background-color: var(--bg-primary);
    border-radius: 4px;
    border: 1px solid var(--border-color);
    box-sizing: border-box;
    transition: background-color 0.2s;
    cursor: pointer;
    width: auto;
    min-width: 120px;
    max-width: 200px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin-bottom: 0;
    flex-shrink: 0;
}

.message-file-item:hover {
    background-color: var(--bg-secondary);
}

.message-file-item .file-icon {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
    color: white;
    font-size: 10px;
    flex-shrink: 0;
}

.message-file-item .file-info {
    flex: 1;
    overflow: hidden;
}

.message-file-item .file-name {
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
    margin-bottom: 0;
}

.message-file-item .file-type {
    font-size: 0.625rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 400;
}
