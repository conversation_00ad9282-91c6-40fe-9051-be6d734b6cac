from src.document_processing.text_chunker import <PERSON><PERSON><PERSON><PERSON>

def test_chunking_logic():
    input_text = (
        "This is a sample paragraph for testing. " * 20 + 
        "\n\nHere is another section with newlines.\nAnd more content.\n\n"
    )

    chunker = TextChunker(chunk_size=100, overlap=20)
    chunks = chunker.chunk_text(input_text)

    assert isinstance(chunks, list), "Chunks should be a list"
    assert all(isinstance(chunk, str) for chunk in chunks), "Chunks must be strings"
    assert all(len(chunk.strip()) > 0 for chunk in chunks), "No empty chunks allowed"
    assert all(len(chunk) <= 100 for chunk in chunks), "Chunk size should respect limit"

    print("✅ Chunking logic passed.")
    print(f"🧩 Total chunks generated: {len(chunks)}")
    for i, c in enumerate(chunks[:3]):
        print(f"Chunk {i+1}: {repr(c[:60])}...")

if __name__ == "__main__":
    test_chunking_logic()
