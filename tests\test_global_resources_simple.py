#!/usr/bin/env python3
"""
Simple test script to verify GlobalResources functionality without external dependencies.
"""
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_global_resources_import():
    """Test that GlobalResources can be imported and basic functionality works."""
    print("🧪 Testing GlobalResources import and basic functionality...")
    
    try:
        from src.core import GlobalResources
        print("✅ GlobalResources imported successfully")
        
        # Test 1: Check initial status
        status = GlobalResources.get_model_status()
        print(f"📊 Initial status: {status}")
        
        # Test 2: Check if methods exist
        methods = [
            'get_unified_model',
            'get_embedding_model', 
            'get_embedding_tokenizer',
            'warm_up_resources',
            'async_warm_up_resources',
            'get_model_status',
            'cleanup',
            'force_reload_models',
            'is_ready'
        ]
        
        for method in methods:
            if hasattr(GlobalResources, method):
                print(f"✅ Method {method} exists")
            else:
                print(f"❌ Method {method} missing")
                return False
        
        # Test 3: Test warm_up_resources (this should work even without models)
        try:
            GlobalResources.warm_up_resources()
            print("✅ warm_up_resources executed successfully")
        except Exception as e:
            print(f"⚠️ warm_up_resources failed (expected if models not available): {e}")
        
        # Test 4: Test get_model_status
        status = GlobalResources.get_model_status()
        print(f"📊 Status after warm-up: {status}")
        
        # Test 5: Test cleanup
        try:
            GlobalResources.cleanup()
            print("✅ cleanup executed successfully")
        except Exception as e:
            print(f"⚠️ cleanup failed: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_core_module_structure():
    """Test that the core module structure is correct."""
    print("\n🧪 Testing core module structure...")
    
    try:
        # Test that core module can be imported
        import src.core
        print("✅ src.core module imported")
        
        # Test that __init__.py exports are correct
        from src.core import GlobalResources
        print("✅ GlobalResources imported from src.core")
        
        # Test that GlobalResources is the expected type
        if hasattr(GlobalResources, 'get_model_status'):
            print("✅ GlobalResources has expected interface")
        else:
            print("❌ GlobalResources missing expected interface")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Core module structure test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting GlobalResources integration tests...\n")
    
    try:
        # Test core module structure
        if not test_core_module_structure():
            print("❌ Core module structure test failed")
            return False
        
        # Test GlobalResources functionality
        if not test_global_resources_import():
            print("❌ GlobalResources functionality test failed")
            return False
        
        print("\n🎉 All tests passed! GlobalResources integration is working correctly.")
        print("\n📝 Note: This test verifies the basic structure and import functionality.")
        print("   For full functionality testing, ensure all dependencies are installed:")
        print("   - sentence-transformers")
        print("   - transformers")
        print("   - torch")
        print("   - qdrant_client")
        
        return True
        
    except Exception as e:
        print(f"\n💥 Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 