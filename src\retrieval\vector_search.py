"""
Vector similarity search for document retrieval using Qdrant.
"""
from typing import List, Dict, Any, Optional
import numpy as np
import time
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type, wait_exponential

from ..utils.logger import get_logger
from ..database.vector_store import QdrantVectorStore, VectorStoreError # Import VectorStoreError
from ..document_processing.embedding_generator import EmbeddingGenerator
from ..config import SIMILARITY_THRESHOLD, MAX_VECTOR_SEARCH_TOP_K # Ensure these are well-defined

logger = get_logger(__name__)

# Define custom exceptions for more granular error handling
class SearchServiceError(Exception):
    """Custom exception for errors specifically within the VectorSearch service."""
    pass

class EmbeddingGenerationError(SearchServiceError):
    """Exception for failures during embedding generation."""
    pass

class VectorStoreSearchError(SearchServiceError):
    """Exception for failures during vector store search."""
    pass

class VectorSearch:
    """Vector similarity search for document retrieval using Qdrant.
    Orchestrates embedding generation and Qdrant search with post-processing.
    """

    def __init__(self,
                 vector_store: Optional[QdrantVectorStore] = None,
                 embedding_generator: Optional[EmbeddingGenerator] = None):
        # Initialize dependencies, allowing for dependency injection for testing
        try:
            self.vector_store = vector_store or QdrantVectorStore()
            self.embedding_generator = embedding_generator or EmbeddingGenerator()
            logger.info("VectorSearch initialized successfully.")
        except Exception as e:
            logger.critical(f"Failed to initialize VectorSearch components: {e}", exc_info=True)
            raise SearchServiceError("Failed to initialize vector search service.") from e

    # Apply exponential backoff for retries, which is generally better for services
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10),
           retry=retry_if_exception_type(Exception), reraise=True)
    def _generate_embedding(self, query: str) -> np.ndarray:
        """Generates an embedding for the given query with retry logic."""
        logger.debug(f"Attempting to generate embedding for query (first 50 chars): '{query[:50]}'")
        try:
            embedding = self.embedding_generator.generate_query_embedding(query)
            if not isinstance(embedding, np.ndarray) or embedding.ndim == 0 or embedding.size == 0:
                raise ValueError("Generated embedding is not a valid numpy array.")
            logger.debug(f"Successfully generated embedding with shape: {embedding.shape}")
            return embedding
        except Exception as e:
            logger.error(f"Failed to generate embedding for query: {str(e)}", exc_info=True)
            raise EmbeddingGenerationError(f"Embedding generation failed: {str(e)}") from e

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10),
           retry=retry_if_exception_type((VectorStoreError, Exception)), reraise=True) # Retry on VectorStoreError
    def _vector_store_search(self, query_embedding: np.ndarray, top_k: int) -> List[Dict[str, Any]]:
        """Performs search against the vector store with retry logic."""
        logger.debug(f"Attempting vector store search with top_k={top_k}")
        try:
            results = self.vector_store.search(query_embedding=query_embedding, top_k=top_k)
            logger.debug(f"Vector store search returned {len(results)} results.")
            return results
        except VectorStoreError as e: # Catch specific VectorStoreError
            logger.error(f"Vector store specific error during search: {str(e)}", exc_info=True)
            raise VectorStoreSearchError(f"Vector store search failed: {str(e)}") from e
        except Exception as e:
            logger.error(f"General error during vector store search: {str(e)}", exc_info=True)
            raise VectorStoreSearchError(f"Vector store search failed: {str(e)}") from e

    def search(self, query: str, top_k: int = 5, prioritize_files: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Search for documents similar to the query, applying relevance filtering and prioritization.

        Args:
            query (str): Query text.
            top_k (int): Number of top documents to return after filtering and boosting (default = 5).
            prioritize_files (List[str], optional): List of source file names (or parts of names)
                                                    to prioritize in the results.

        Returns:
            List[Dict[str, Any]]: A list of relevant documents with metadata and similarity scores,
                                  sorted by score. Returns an empty list on error or no relevant results.
        """
        search_start_time = time.time()
        prioritize_files = prioritize_files or [] # Ensure it's a list even if None

        if not isinstance(query, str) or not query.strip():
            logger.warning("Invalid or empty query received for search.", extra={'query': query})
            return []

        logger.info(f"Initiating search for query: '{query[:100]}{'...' if len(query) > 100 else ''}'", extra={
            'requested_top_k': top_k,
            'prioritize_files_count': len(prioritize_files)
        })

        try:
            # 1. Generate Query Embedding
            query_embedding = self._generate_embedding(query)

            # 2. Search Vector Store (fetch more than final top_k for better filtering)
            # Fetch at least 'top_k' or up to MAX_VECTOR_SEARCH_TOP_K to ensure enough candidates
            search_top_k_candidates = max(top_k * 2, top_k, MAX_VECTOR_SEARCH_TOP_K)
            results = self._vector_store_search(query_embedding=query_embedding, top_k=search_top_k_candidates)

            if not results:
                logger.info("No initial search results returned from Qdrant.", extra={'query': query[:50]})
                return []

            # 3. Validate and Prepare Results
            valid_results = []
            for i, doc in enumerate(results):
                # Ensure essential fields are present and valid for processing
                if not isinstance(doc, dict) or "score" not in doc or not isinstance(doc["score"], (int, float)):
                    logger.warning(f"Skipping invalid search result (missing/bad score) at index {i}: {doc}", extra={'query': query[:50]})
                    continue
                valid_results.append(doc)

            if not valid_results:
                logger.warning("All initial search results were invalid after validation.", extra={'query': query[:50]})
                return []

            # 4. Dynamic Thresholding and Filtering
            # Use max score from valid results to set a more adaptive threshold
            max_overall_score = max(doc["score"] for doc in valid_results)
            # Ensure dynamic threshold is at least SIMILARITY_THRESHOLD
            dynamic_effective_threshold = max(SIMILARITY_THRESHOLD, max_overall_score * 0.7)

            filtered_results_by_threshold = [doc for doc in valid_results if doc["score"] >= dynamic_effective_threshold]

            # Fallback if dynamic filtering is too aggressive (e.g., all good scores are slightly below 0.7*max_score)
            if len(filtered_results_by_threshold) < 2 and len(valid_results) >= 2:
                logger.info(f"Dynamic threshold ({dynamic_effective_threshold:.3f}) was too aggressive, falling back to static threshold ({SIMILARITY_THRESHOLD:.3f}).")
                filtered_results_by_threshold = [doc for doc in valid_results if doc["score"] >= SIMILARITY_THRESHOLD]
            
            # If still no results, or very few, we might consider a lower bound if needed, or return empty.
            # For now, let's stick to the defined thresholds.
            if not filtered_results_by_threshold:
                 logger.info(f"No results passed similarity thresholds (static {SIMILARITY_THRESHOLD:.3f}, dynamic {dynamic_effective_threshold:.3f}).", extra={'query': query[:50]})
                 return []

            # 5. Prioritize Files (Boost Scores)
            processed_results = []
            for doc in filtered_results_by_threshold:
                doc_copy = doc.copy() # Work on a copy to avoid modifying original Qdrant result dict directly if it's reused
                source_file = doc_copy.get("source_file", "")
                is_prioritized = False
                for pf in prioritize_files:
                    if pf and pf in source_file: # Check if pf is not empty string and is a substring
                        doc_copy["score"] = min(1.0, doc_copy["score"] * 1.2) # Apply boost
                        is_prioritized = True
                        logger.debug(f"Boosted score for prioritized file: {source_file}, new score: {doc_copy['score']:.3f}")
                        break # Only boost once per file
                doc_copy["prioritized"] = is_prioritized
                processed_results.append(doc_copy)

            # 6. Sort and Truncate to top_k
            processed_results.sort(key=lambda x: x["score"], reverse=True)
            final_results = processed_results[:top_k]

            # 7. Logging and Return
            elapsed_time = time.time() - search_start_time
            
            logger.info("Search completed successfully.", extra={
                'query_preview': query[:100],
                'final_results_count': len(final_results),
                'total_candidates_from_qdrant': len(results),
                'processing_time_seconds': round(elapsed_time, 3),
                'top_score': final_results[0]['score'] if final_results else None,
                'min_score_in_final_results': final_results[-1]['score'] if final_results and len(final_results) > 1 else None,
                'prioritized_files_matched_count': sum(1 for d in final_results if d.get("prioritized", False))
            })

            return final_results

        except EmbeddingGenerationError as e:
            logger.error(f"Search failed due to embedding generation error: {e}", exc_info=True)
            return []
        except VectorStoreSearchError as e:
            logger.error(f"Search failed due to vector store error: {e}", exc_info=True)
            return []
        except Exception as e:
            logger.error(f"An unexpected error occurred during search: {e}", exc_info=True)
            return []