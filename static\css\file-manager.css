/* File Manager Styles */
/* Apply consistent font family */
.file-list-container,
.file-item,
.file-name,
.file-type,
.file-preview-title,
.page-number,
.more-pages,
.preview-error {
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}
.file-list-container {
    display: none; /* Hide by default */
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
    width: 100%;
    max-height: 120px;
    overflow-y: auto;
    padding: 8px;
    border-radius: 8px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

/* Only show the file list container when it has content */
.file-list-container:not(:empty) {
    display: flex;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    border-radius: 6px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    max-width: 250px;
    position: relative;
    transition: all 0.2s ease;
}

.file-item:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.file-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    color: white;
    flex-shrink: 0;
}

.file-info {
    flex: 1;
    min-width: 0;
    margin-right: 8px;
}

.file-name {
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
}

.file-type {
    font-size: 0.625rem;
    color: var(--text-secondary);
    font-weight: 400;
}

.file-actions {
    display: flex;
    gap: 4px;
}

.file-preview, .file-remove {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 2px;
    border-radius: 4px;
    font-size: 0.75rem;
    transition: color 0.2s;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.file-preview:hover {
    color: var(--accent-color);
}

.file-remove:hover {
    color: var(--error-color);
}

/* File status indicators */
.file-item.uploading {
    border-color: var(--accent-color);
}

.file-item.uploading .file-name::after {
    content: " (uploading...)";
    color: var(--accent-color);
    font-size: 0.625rem;
    font-weight: 400;
}

.file-item.success {
    border-color: var(--border-color);
}

.file-item.error {
    border-color: var(--error-color);
}

.file-item.error .file-name::after {
    content: " (error)";
    color: var(--error-color);
    font-size: 0.625rem;
    font-weight: 400;
}

.file-item.locked {
    opacity: 0.8;
    pointer-events: none;
}

.file-item.locked .file-remove {
    display: none;
}

/* File Preview Modal */
.file-preview-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.file-preview-content {
    background-color: var(--bg-primary); /* Alabaster White background */
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.file-preview-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-preview-title {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
}

.file-preview-close {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 18px;
}

.file-preview-container {
    padding: 16px;
    overflow-y: auto;
    flex: 1;
}

/* Preview content styles */
.pdf-preview {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
}

.pdf-preview canvas {
    max-width: 100%;
    height: auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-number {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 4px;
    font-weight: 400;
}

.more-pages {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 16px;
    text-align: center;
    font-weight: 400;
}

.docx-preview {
    padding: 16px;
    background-color: var(--bg-primary);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    color: var(--text-primary);
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
    line-height: 1.5;
}

.txt-preview {
    white-space: pre-wrap;
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    padding: 16px;
    background-color: var(--bg-secondary);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.preview-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32px;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 400;
}

.preview-loading i {
    margin-right: 8px;
    animation: spin 1s linear infinite;
}

.preview-error {
    padding: 32px;
    text-align: center;
    color: var(--error-color);
    font-size: 0.875rem;
    font-weight: 400;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
