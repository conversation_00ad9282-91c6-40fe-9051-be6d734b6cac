import os
import bcrypt
import jwt
import pyotp
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from ..utils.logger import get_logger
from ..database.user_db import UserModel
load_dotenv()
logger = get_logger(__name__)

# --- JWT Configuration ---
JWT_SECRET = os.getenv("JWT_SECRET")
if not JWT_SECRET:
    raise ValueError("JWT_SECRET environment variable is required for security.")

JWT_ALGORITHM = 'HS256'
JWT_EXPIRATION_HOURS = 24


class AuthService:
    """Production-grade authentication service with 2FA support."""

    def __init__(self):
        self.user_model = UserModel()

    def hash_password(self, password: str) -> str:
        try:
            return bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()
        except Exception as e:
            logger.exception("Error hashing password")
            raise

    def verify_password(self, stored_password: str, provided_password: str) -> bool:
        try:
            return bcrypt.checkpw(provided_password.encode(), stored_password.encode())
        except Exception as e:
            logger.exception("Error verifying password")
            return False

    def generate_2fa_secret(self) -> str:
        return pyotp.random_base32()

    def get_2fa_qr_url(self, email: str, secret: str, issuer: str = "AdvancedRAGChatbot") -> str:
        totp = pyotp.TOTP(secret)
        return totp.provisioning_uri(name=email, issuer_name=issuer)

    def verify_2fa_code(self, secret: str, code: str) -> bool:
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(code, valid_window=1)
        except Exception as e:
            logger.exception("Error verifying 2FA code")
            return False

    def register_user(self, email: str, password: str, full_name: str, employee_id: Optional[str] = None) -> Dict[str, Any]:
        try:
            existing_user = self.user_model.get_user_by_email(email)
            if existing_user:
                return {"success": False, "message": "User with this email already exists"}

            password_hash = self.hash_password(password)
            two_fa_secret = self.generate_2fa_secret()

            user_id = self.user_model.create_user(
                email=email,
                password_hash=password_hash,
                full_name=full_name,
                employee_id=employee_id,
                two_fa_secret=two_fa_secret
            )

            if user_id:
                qr_url = self.get_2fa_qr_url(email, two_fa_secret)
                return {
                    "success": True,
                    "message": "User registered successfully",
                    "user_id": user_id,
                    "2fa_qr_url": qr_url
                }
            else:
                return {"success": False, "message": "Failed to register user"}
        except Exception as e:
            logger.exception("Error registering user")
            return {"success": False, "message": f"An error occurred: {str(e)}"}

    def login_user(self, email: str, password: str, two_fa_code: Optional[str] = None) -> Dict[str, Any]:
        try:
            user = self.user_model.get_user_by_email(email)
            if not user:
                return {"success": False, "message": "Invalid email or password"}

            if not self.verify_password(user['password_hash'], password):
                return {"success": False, "message": "Invalid email or password"}

            if 'two_fa_secret' in user:
                if not two_fa_code:
                    return {"success": False, "message": "2FA code required"}
                if not self.verify_2fa_code(user['two_fa_secret'], two_fa_code):
                    return {"success": False, "message": "Invalid 2FA code"}

            self.user_model.update_last_login(user['id'])
            token = self.generate_token(user)

            return {
                "success": True,
                "message": "Login successful",
                "token": token,
                "user": {
                    "id": user['id'],
                    "email": user['email'],
                    "full_name": user['full_name'],
                    "employee_id": user.get('employee_id')
                }
            }
        except Exception as e:
            logger.exception("Error during login")
            return {"success": False, "message": f"An error occurred: {str(e)}"}

    def generate_token(self, user: Dict[str, Any]) -> str:
        payload = {
            'user_id': user['id'],
            'email': user['email'],
            'exp': datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
        }
        try:
            return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)
        except Exception as e:
            logger.exception("Error generating JWT token")
            raise

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
            user_id = payload.get('user_id')
            if user_id:
                return self.user_model.get_user_by_id(user_id)
            return None
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token has expired")
            return None
        except jwt.InvalidTokenError:
            logger.warning("Invalid JWT token")
            return None
        except Exception as e:
            logger.exception("Error verifying JWT token")
            return None
