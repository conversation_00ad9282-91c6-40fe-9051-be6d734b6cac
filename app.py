"""
Main application entry point for the Advanced HR Assistant Chatbot.
Optimized for performance with local document processing and vector search.
"""
import os
import time
import asyncio
from pathlib import Path
from dotenv import load_dotenv
import multiprocessing

# Must be at the very top for Windows multiprocessing
multiprocessing.set_start_method('spawn', force=True)

# Load environment variables from .env file
load_dotenv()

# Validate required environment variables
if not os.getenv("GROQ_API_KEY"):
    raise ValueError("GROQ_API_KEY is not set in the environment or .env file")

# Import all other modules
from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
from markdown import markdown
from src.utils.logger import get_logger
from src.chain.chain_builder import ChainBuilder
from src.conversation.history_manager import HistoryManager
from src.speech.speech_to_text import SpeechToText
from src.speech.text_to_speech import TextToSpeech
from src.document_processing.training_pipeline import TrainingPipeline
from src.user_authentication.user_authorisation import AuthService
from src.utils.email_service import EmailService
from src.config import HR_EMAILS, ENABLE_EMAIL_ESCALATION, GROQ_API_KEY

# Initialize logger
logger = get_logger(__name__)

# Global variables for lazy initialization
_chain_builder = None
_history_manager = None
_speech_to_text = None
_text_to_speech = None
_auth_service = None
_email_service = None
_session_history = {}
app_state = {}

def get_chain_builder():
    global _chain_builder
    if _chain_builder is None:
        _chain_builder = ChainBuilder()
    return _chain_builder

def get_history_manager():
    global _history_manager
    if _history_manager is None:
        _history_manager = HistoryManager()
    return _history_manager

def get_speech_to_text():
    global _speech_to_text
    if _speech_to_text is None:
        _speech_to_text = SpeechToText()
    return _speech_to_text

def get_text_to_speech():
    global _text_to_speech
    if _text_to_speech is None:
        _text_to_speech = TextToSpeech()
    return _text_to_speech

def get_auth_service():
    global _auth_service
    if _auth_service is None:
        _auth_service = AuthService()
    return _auth_service

def get_email_service():
    global _email_service
    if _email_service is None:
        _email_service = EmailService()
    return _email_service

def run_async_in_sync(coro):
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, coro)
                return future.result()
        else:
            return loop.run_until_complete(coro)
    except RuntimeError:
        return asyncio.run(coro)

def initialize_services():
    if os.environ.get("WERKZEUG_RUN_MAIN") == "true":
        logger.info("Initializing core services...")

        get_chain_builder()
        get_history_manager()

        if ENABLE_EMAIL_ESCALATION and HR_EMAILS:
            get_email_service()

        try:
            pipeline = TrainingPipeline()
            app_state["pipeline"] = pipeline

            # HR file ingestion removed from app startup for production best practices.

        except AttributeError as e:
            logger.warning("🔁 process_hr_files() not found in TrainingPipeline", extra={"error": str(e)})
        except Exception as e:
            import traceback
            error_msg = f"Error during HR document ingestion: {e}\n{traceback.format_exc()}"
            print(error_msg)
            logger.error("Error during HR document ingestion", extra={
                "error": str(e),
                "traceback": traceback.format_exc()
            })


def create_app():
    """Create and configure the Flask application."""
    app = Flask(__name__,
               static_folder=os.path.join(os.path.dirname(__file__), "static"),
               template_folder=os.path.join(os.path.dirname(__file__), "templates"))
    CORS(app)  # Enable CORS for all routes

    # Set a secret key for session management
    app.secret_key = os.environ.get('FLASK_SECRET_KEY', os.urandom(24))

    @app.route("/")
    def index():
        """Render the main application page."""
        return render_template("index.html")

    @app.route("/api/query", methods=["POST"])
    def query():
        """Process a text query with proper document verification."""
        try:
            data = request.json
            user_query = data["query"]
            device_id = data["device_id"]
            files_info = data.get("files_info", [])

            if files_info:
                pipeline = TrainingPipeline()
                for file_info in files_info:
                    if not pipeline.is_file_processed(file_info.get("name")):
                        return jsonify({
                            "error": f"File {file_info.get('name')} not processed. Please re-upload.",
                            "success": False
                        }), 400

            query_start_time = time.time()
            chain_builder = get_chain_builder()

            doc_count = chain_builder.get_vector_database_count()
            if doc_count == 0:
                logger.warning("No documents in vector database")

            if hasattr(chain_builder, 'run_chain_sync'):
                result = chain_builder.run_chain_sync(user_query, device_id, files_info=files_info)
            else:
                result = run_async_in_sync(chain_builder.run_chain(user_query, device_id, files_info=files_info))

            if isinstance(result, dict) and result.get("sources"):
                logger.info(f"Query used {len(result['sources'])} document sources")
            else:
                logger.warning("Query result contains no document sources - RAG may not be working")

            response_content = result.get("content", "") if isinstance(result, dict) else ""

            if not response_content.strip():
                response_content = "I'm sorry, I couldn't find relevant information in the available documents. Please ensure your documents are properly uploaded and processed."

            final_result = {
                "response": markdown(response_content),
                "sources": result.get("sources", []) if isinstance(result, dict) else [],
                "language": result.get("language", "en") if isinstance(result, dict) else "en",
                "response_time": time.time() - query_start_time,
                "document_count": doc_count
            }

            return jsonify(final_result)

        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return jsonify({
                "error": "Query processing failed",
                "message": "Please check if documents are properly uploaded and try again."
            }), 500

    @app.route("/api/speech-to-text", methods=["POST"])
    def speech_to_text_api():
        """Convert speech to text."""
        try:
            audio_data = request.files.get("audio")

            if audio_data:
                temp_path = Path(app.root_path) / "temp_audio.wav"
                audio_data.save(temp_path)
                text = "Audio file processing not implemented yet"
            else:
                text = get_speech_to_text().recognize_speech()

            return jsonify({"text": text})

        except Exception as e:
            logger.error(f"Error in speech-to-text: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/upload-document", methods=["POST"])
    def upload_document():
        """Upload and immediately process a document."""
        try:
            import traceback
            if "file" not in request.files:
                return jsonify({"error": "No file uploaded", "success": False}), 400

            file = request.files["file"]
            if file.filename == "":
                return jsonify({"error": "No file selected", "success": False}), 400

            raw_dir = Path(app.root_path) / "data" / "raw"
            os.makedirs(raw_dir, exist_ok=True)
            file_path = raw_dir / file.filename
            file.save(file_path)

            pipeline = TrainingPipeline()
            success = pipeline.process_file(file_path, force_reprocess=False)

            if not success:
                return jsonify({
                    "error": "Document processing failed",
                    "success": False
                }), 500

            vector_count = 0

            return jsonify({
                "success": True,
                "filename": file.filename,
                "processed": True,
                "vector_count": vector_count
            })

        except Exception as e:
            logger.error(f"Error uploading/processing document: {e}")
            traceback.print_exc()
            return jsonify({"error": str(e), "success": False}), 500

    @app.route("/api/clear-history", methods=["POST"])
    def clear_history():
        """Clear conversation history for a device."""
        try:
            data = request.json
            device_id = data["device_id"]

            history_manager = get_history_manager()
            history_manager.clear_history(device_id)

            if device_id in _session_history:
                del _session_history[device_id]

            return jsonify({"success": True})

        except Exception as e:
            logger.error(f"Error clearing history: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/confirm-escalation", methods=["POST"])
    def confirm_escalation():
        """Confirm escalation and send email to HR."""
        try:
            data = request.json
            user_query = data["query"]
            device_id = data["device_id"]

            history = get_history_manager().get_history(device_id)

            if not ENABLE_EMAIL_ESCALATION:
                return jsonify({
                    "success": False,
                    "message": "Email escalation is not enabled"
                }), 400

            if not HR_EMAILS:
                return jsonify({
                    "success": False,
                    "message": "No HR email addresses configured"
                }), 400

            result = get_email_service().send_escalation_email(
                hr_emails=HR_EMAILS,
                user_query=user_query,
                conversation_history=history
            )

            if result["success"]:
                return jsonify({
                    "success": True,
                    "message": "Your question has been escalated to the HR team. They will follow up with you directly."
                })
            else:
                logger.error(f"Error sending escalation email: {result['message']}")
                return jsonify({
                    "success": False,
                    "message": "There was an error escalating your question. Please try again later."
                }), 500

        except Exception as e:
            logger.error(f"Error confirming escalation: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/file-preview", methods=["GET"])
    def file_preview():
        """Get file content for preview."""
        try:
            filename = request.args.get("filename")
            if not filename:
                return jsonify({"error": "No filename provided"}), 400

            raw_dir = Path(app.root_path) / "data" / "raw"
            file_path = raw_dir / filename

            if not file_path.exists():
                return jsonify({"error": f"File {filename} not found"}), 404

            file_extension = file_path.suffix.lower()

            if file_extension in ['.txt', '.md']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return jsonify({
                    "success": True,
                    "content": content,
                    "content_type": "text"
                })
            elif file_extension in ['.pdf', '.docx']:
                return jsonify({
                    "success": True,
                    "file_path": str(file_path),
                    "content_type": file_extension[1:]
                })
            else:
                return jsonify({
                    "error": f"Unsupported file type: {file_extension}"
                }), 400

        except Exception as e:
            logger.error(f"Error getting file preview: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/health/documents", methods=["GET"])
    def document_health_check():
        """Check document processing and vector database health."""
        try:
            pipeline = TrainingPipeline()
            chain_builder = get_chain_builder()

            raw_files = len(list(Path(app.root_path).glob("data/raw/*")))
            processed_files = len(list(Path(app.root_path).glob("data/processed/*")))
            vector_count = chain_builder.get_vector_database_count() if hasattr(chain_builder, 'get_vector_database_count') else 0

            processing_healthy = processed_files > 0 and vector_count > 0

            return jsonify({
                "success": True,
                "raw_files": raw_files,
                "processed_files": processed_files,
                "vector_embeddings": vector_count,
                "processing_healthy": processing_healthy,
                "recommendations": [
                    "Upload documents if none are processed",
                    "Check document processing logs if embeddings are 0",
                    "Restart application if processing appears stuck"
                ] if not processing_healthy else []
            })

        except Exception as e:
            logger.error(f"Document health check failed: {e}")
            return jsonify({
                "success": False,
                "error": str(e)
            }), 500

    @app.route("/api/process-hr-files", methods=["POST"])
    def process_hr_files():
        """Process HR files from a directory."""
        try:
            data = request.json
            directory = data.get("directory", "Hr Files")
            force_reprocess = data.get("force_reprocess", False)

            pipeline = TrainingPipeline()
            num_processed = pipeline.process_hr_files(Path(directory), force_reprocess=force_reprocess)

            return jsonify({
                "success": True,
                "files_processed": num_processed,
                "force_reprocess": force_reprocess
            })

        except Exception as e:
            logger.error(f"Error processing HR files: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/register", methods=["POST"])
    def register():
        """Register a new user."""
        try:
            data = request.json
            email = data.get("email")
            password = data.get("password")
            full_name = data.get("full_name")
            employee_id = data.get("employee_id")

            if not all([email, password, full_name]):
                return jsonify({
                    "success": False,
                    "message": "Email, password, and full name are required"
                }), 400

            result = get_auth_service().register_user(
                email=email,
                password=password,
                full_name=full_name,
                employee_id=employee_id
            )

            if result["success"]:
                return jsonify(result), 201
            else:
                return jsonify(result), 400

        except Exception as e:
            logger.error(f"Error registering user: {e}")
            return jsonify({
                "success": False,
                "message": f"An error occurred: {str(e)}"
            }), 500

    @app.route("/api/login", methods=["POST"])
    def login():
        """Authenticate a user."""
        try:
            data = request.json
            email = data.get("email")
            password = data.get("password")

            if not all([email, password]):
                return jsonify({
                    "success": False,
                    "message": "Email and password are required"
                }), 400

            result = get_auth_service().login_user(
                email=email,
                password=password,
                two_fa_code=data.get("two_fa_code")
            )

            if result["success"]:
                session["user_id"] = result["user"]["id"]
                session["email"] = result["user"]["email"]
                return jsonify(result), 200
            else:
                # If 2FA is required, always return the QR code URL if available
                if result.get("message") == "2FA code required":
                    user = get_auth_service().user_model.get_user_by_email(email)
                    if user and user.get("two_fa_secret"):
                        # Use 'Ziantrix' as the issuer and name for the QR code
                        qr_url = get_auth_service().get_2fa_qr_url(email, user["two_fa_secret"], issuer="Ziantrix")
                        result["2fa_qr_url"] = qr_url
                return jsonify(result), 401

        except Exception as e:
            logger.error(f"Error logging in user: {e}")
            return jsonify({
                "success": False,
                "message": f"An error occurred: {str(e)}"
            }), 500

    @app.route("/api/logout", methods=["POST"])
    def logout():
        """Log out a user."""
        try:
            session.clear()
            return jsonify({
                "success": True,
                "message": "Logged out successfully"
            }), 200

        except Exception as e:
            logger.error(f"Error logging out user: {e}")
            return jsonify({
                "success": False,
                "message": f"An error occurred: {str(e)}"
            }), 500

    @app.route("/api/user", methods=["GET"])
    def get_user():
        """Get the current user's information."""
        try:
            user_id = session.get("user_id")
            if not user_id:
                return jsonify({
                    "success": False,
                    "message": "Not authenticated"
                }), 401

            user = get_auth_service().user_model.get_user_by_id(user_id)
            if not user:
                session.clear()
                return jsonify({
                    "success": False,
                    "message": "User not found"
                }), 404

            return jsonify({
                "success": True,
                "user": {
                    "id": user["id"],
                    "email": user["email"],
                    "full_name": user["full_name"],
                    "company_name": user["company_name"]
                }
            }), 200

        except Exception as e:
            logger.error(f"Error getting user: {e}")
            return jsonify({
                "success": False,
                "message": f"An error occurred: {str(e)}"
            }), 500

    @app.route("/api/hr-representatives", methods=["GET"])
    def get_hr_representatives():
        """Get list of HR representatives."""
        try:
            hr_reps = [
                {"id": "hr1", "name": "John Smith", "email": "<EMAIL>", "department": "HR Operations"},
                {"id": "hr2", "name": "Sarah Johnson", "email": "<EMAIL>", "department": "Employee Relations"},
                {"id": "hr3", "name": "Michael Brown", "email": "<EMAIL>", "department": "Benefits"}
            ]
            return jsonify({"success": True, "representatives": hr_reps})
        except Exception as e:
            logger.error(f"Error getting HR representatives: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/submit-escalation", methods=["POST"])
    def submit_escalation():
        """Submit a new HR escalation."""
        try:
            data = request.json
            required_fields = ["hrPerson", "issueType", "issueDescription", "priority"]

            for field in required_fields:
                if field not in data:
                    return jsonify({
                        "success": False,
                        "message": f"Missing required field: {field}"
                    }), 400

            user_details = session.get("user_details", {})
            if not user_details:
                return jsonify({
                    "success": False,
                    "message": "User not authenticated"
                }), 401

            escalation_data = {
                "user_id": user_details.get("id"),
                "user_name": user_details.get("name"),
                "user_email": user_details.get("email"),
                "hr_person": data["hrPerson"],
                "issue_type": data["issueType"],
                "description": data["issueDescription"],
                "priority": data["priority"],
                "status": "pending",
                "created_at": time.time()
            }

            logger.info(f"New escalation created: {escalation_data}")

            if ENABLE_EMAIL_ESCALATION:
                email_service = get_email_service()
                hr_email = next((rep["email"] for rep in get_hr_representatives().json["representatives"]
                               if rep["id"] == data["hrPerson"]), None)

                if hr_email:
                    email_service.send_escalation_email(
                        hr_emails=[hr_email],
                        user_query=f"New HR Escalation: {data['issueType']} - {data['issueDescription']}",
                        conversation_history=[{
                            "user_query": f"Priority: {data['priority']}\nIssue Type: {data['issueType']}\nDescription: {data['issueDescription']}",
                            "assistant_response": f"Escalated to: {data['hrPerson']}"
                        }]
                    )

            return jsonify({
                "success": True,
                "message": "Your issue has been escalated to HR. They will contact you shortly.",
                "escalation_id": f"ESC-{int(time.time())}"
            })

        except Exception as e:
            logger.error(f"Error submitting escalation: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/chats/<chat_id>", methods=["GET"])
    def get_chat(chat_id):
        """Get paginated messages for a specific chat."""
        try:
            page = int(request.args.get("page", 1))
            page_size = int(request.args.get("page_size", 20))

            history_manager = get_history_manager()
            messages = history_manager.get_chat_messages(chat_id, page, page_size)

            total_messages = history_manager.get_chat_message_count(chat_id)

            return jsonify({
                "success": True,
                "messages": messages,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_messages": total_messages,
                    "total_pages": (total_messages + page_size - 1) // page_size
                }
            })

        except Exception as e:
            logger.error(f"Error getting chat messages: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/chats/<chat_id>/count", methods=["GET"])
    def get_chat_message_count_api(chat_id):
        """Get total number of messages for a specific chat."""
        try:
            history_manager = get_history_manager()
            total_messages = history_manager.get_chat_message_count(chat_id)
            return jsonify({
                "success": True,
                "count": total_messages
            })
        except Exception as e:
            logger.error(f"Error getting chat message count: {e}")
            return jsonify({"error": str(e)}), 500

    return app

if __name__ == "__main__":
    try:
        initialize_services()
        flask_app = create_app()
        flask_app.run(host="0.0.0.0", port=5000, debug=True)
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        raise
