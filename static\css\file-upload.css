/* File Upload and Preview Styles */
/* Apply consistent font family */
.file-list-container,
.file-item,
.file-item .file-name,
.file-item .file-type,
.preview-loading,
.preview-error {
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

/* File List Container */
.file-list-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
    width: 100%;
    max-height: 150px;
    overflow-y: auto;
    padding: 5px 0;
}

/* File Item */
.file-item {
    display: flex;
    align-items: center;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 6px 10px;
    position: relative;
    box-shadow: 0 1px 2px var(--shadow-color);
    transition: all 0.2s ease;
    cursor: pointer;
    max-width: 200px;
    font-size: 0.875rem;
}

.file-item:hover {
    box-shadow: 0 2px 5px var(--shadow-color);
}

.file-item.locked {
    opacity: 0.8;
    cursor: default;
}

.file-item .file-icon {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 8px;
    flex-shrink: 0;
    font-size: 12px;
}

.file-item .file-info {
    flex: 1;
    overflow: hidden;
    max-width: 120px;
}

.file-item .file-name {
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
}

.file-item .file-type {
    font-size: 0.625rem;
    color: var(--text-secondary);
    font-weight: 400;
}

.file-item .file-actions {
    display: flex;
    align-items: center;
    margin-left: 5px;
}

.file-item .file-remove {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 3px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.file-item .file-remove:hover {
    color: var(--error-color);
    background-color: rgba(220, 53, 69, 0.1);
}

.file-item.locked .file-remove {
    display: none;
}

/* File Preview Modal */
.modal-lg {
    width: 80%;
    max-width: 800px;
    height: 80%;
    max-height: 600px;
}

.file-preview-container {
    width: 100%;
    height: 100%;
    min-height: 400px;
    overflow: auto;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 10px;
    background-color: var(--bg-primary); /* Alabaster White background */
}

/* PDF Preview */
.pdf-preview {
    width: 100%;
    height: 100%;
    min-height: 400px;
    overflow: auto;
}

.pdf-preview canvas {
    display: block;
    margin: 0 auto 10px auto;
    border: 1px solid var(--border-color);
}

/* DOCX Preview */
.docx-preview {
    width: 100%;
    height: 100%;
    overflow: auto;
    padding: 20px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.5;
}

/* TXT Preview */
.txt-preview {
    width: 100%;
    height: 100%;
    overflow: auto;
    padding: 10px;
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    line-height: 1.4;
    font-size: 0.875rem;
}

/* Loading indicator */
.preview-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
}

.preview-loading i {
    margin-right: 8px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error message */
.preview-error {
    padding: 20px;
    color: var(--error-color);
    text-align: center;
    font-size: 0.875rem;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
}

/* Error toast notification */
.toast-notification.error-toast {
    background-color: #F44336 !important; /* Red for error */
    color: white !important;
    border-left: 4px solid #D32F2F !important; /* Darker red border */
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) translateY(-100px) !important;
    bottom: auto !important;
    right: auto !important;
    min-width: 300px !important;
    max-width: 80% !important;
    text-align: center !important;
    transition: transform 0.3s ease !important;
}

.toast-notification.error-toast.show {
    transform: translateX(-50%) translateY(0) !important;
}
