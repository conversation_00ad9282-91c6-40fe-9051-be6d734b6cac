# Production Deployment Guide

## Overview

This guide covers deploying the Multi-Model RAG Chatbot with production-grade logging in enterprise environments.

## 🚀 Production-Grade Features

### ✅ **What's Production-Ready**

1. **Concurrent File Handling**
   - Thread-safe logging with file locking
   - Prevents permission errors on Windows/Linux
   - Automatic fallback mechanisms

2. **Security & Compliance**
   - PII/PHI data masking
   - Configurable encryption
   - Audit trail support
   - GDPR/CCPA compliance features

3. **Performance Monitoring**
   - Real-time performance metrics
   - Queue health monitoring
   - Bottleneck detection
   - Circuit breaker pattern

4. **Reliability**
   - Graceful error handling
   - Automatic recovery
   - Health checks
   - Structured error codes

5. **Scalability**
   - Async queue-based logging
   - Configurable buffer sizes
   - Memory-efficient operations

## 📋 Pre-Deployment Checklist

### Environment Setup
- [ ] Python 3.8+ installed
- [ ] Virtual environment configured
- [ ] All dependencies installed
- [ ] Log directory permissions set
- [ ] Environment variables configured

### Security Configuration
- [ ] Encryption keys set (if using encryption)
- [ ] PII masking enabled
- [ ] Audit logging configured
- [ ] Network security configured

### Monitoring Setup
- [ ] Health check endpoints configured
- [ ] Performance monitoring enabled
- [ ] Alert thresholds set
- [ ] Log aggregation configured

## 🔧 Configuration

### Environment Variables

```bash
# Basic Configuration
LOG_LEVEL=INFO
LOG_DIR=/var/log/chatbot
LOG_FILENAME=application.log
LOG_MAX_BYTES=52428800  # 50MB
LOG_BACKUP_COUNT=10

# Production Features
LOG_ENABLE_COMPRESSION=true
LOG_ENABLE_ENCRYPTION=false  # Enable if needed
LOG_ENCRYPTION_KEY=your-secure-key-here
LOG_ENABLE_PII_MASKING=true
LOG_RETENTION_DAYS=90

# Performance
LOG_QUEUE_SIZE=10000
LOG_ENABLE_PERFORMANCE_MONITORING=true
LOG_PERFORMANCE_ALERT_THRESHOLD_MS=100

# Compliance
LOG_ENABLE_AUDIT=true
LOG_AUDIT_PATH=/var/log/chatbot/audit
```

### Production Configuration Example

```python
from src.utils.logger import LoggingConfig, LogLevel, LogFormat, RotationType
from pathlib import Path

config = LoggingConfig(
    # Basic settings
    log_dir=Path("/var/log/chatbot"),
    log_filename="application.log",
    rotation_type=RotationType.BOTH,
    max_bytes=50 * 1024 * 1024,  # 50MB
    backup_count=10,
    
    # Log levels
    default_level=LogLevel.INFO,
    file_level=LogLevel.DEBUG,
    console_level=LogLevel.WARNING,
    
    # Formats
    file_format=LogFormat.JSON,
    console_format=LogFormat.TEXT,
    
    # Production features
    enable_compression=True,
    enable_encryption=False,  # Enable if required
    enable_pii_masking=True,
    enable_performance_monitoring=True,
    performance_alert_threshold_ms=100.0,
    
    # Compliance
    enable_audit_logging=True,
    audit_log_path=Path("/var/log/chatbot/audit"),
    retention_days=90,
    
    # Performance
    queue_size=10000,
    queue_timeout=1.0,
    listener_timeout=5.0
)
```

## 🏗️ Deployment Options

### 1. Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY . .

# Create log directory
RUN mkdir -p /var/log/chatbot && \
    chmod 755 /var/log/chatbot

# Set environment variables
ENV LOG_DIR=/var/log/chatbot
ENV LOG_LEVEL=INFO
ENV LOG_ENABLE_PII_MASKING=true

# Run application
CMD ["python", "app.py"]
```

### 2. Systemd Service

```ini
[Unit]
Description=Multi-Model RAG Chatbot
After=network.target

[Service]
Type=simple
User=chatbot
WorkingDirectory=/opt/chatbot
Environment=LOG_DIR=/var/log/chatbot
Environment=LOG_LEVEL=INFO
Environment=LOG_ENABLE_PII_MASKING=true
ExecStart=/opt/chatbot/venv/bin/python app.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 3. Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatbot
spec:
  replicas: 3
  selector:
    matchLabels:
      app: chatbot
  template:
    metadata:
      labels:
        app: chatbot
    spec:
      containers:
      - name: chatbot
        image: chatbot:latest
        env:
        - name: LOG_DIR
          value: "/var/log/chatbot"
        - name: LOG_LEVEL
          value: "INFO"
        - name: LOG_ENABLE_PII_MASKING
          value: "true"
        volumeMounts:
        - name: logs
          mountPath: /var/log/chatbot
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: chatbot-logs-pvc
```

## 📊 Monitoring & Observability

### Health Checks

```python
# Health check endpoint
@app.route("/health/logging")
def logging_health():
    health_status = get_health_status()
    return jsonify(health_status)
```

### Metrics to Monitor

1. **Performance Metrics**
   - Average write time
   - Queue utilization
   - Error rates
   - Memory usage

2. **Operational Metrics**
   - Log file sizes
   - Rotation frequency
   - Compression ratios
   - Disk usage

3. **Security Metrics**
   - PII masking events
   - Encryption failures
   - Audit log entries
   - Access violations

### Alerting Rules

```yaml
# Prometheus alerting rules
groups:
- name: logging_alerts
  rules:
  - alert: HighLogWriteTime
    expr: log_write_time_ms > 100
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Log write time is high"
      
  - alert: LogQueueFull
    expr: log_queue_utilization > 0.8
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Log queue is nearly full"
      
  - alert: LogErrors
    expr: log_error_rate > 0.05
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High log error rate detected"
```

## 🔒 Security Considerations

### Data Protection

1. **PII Masking**
   - Automatically masks sensitive data
   - Configurable patterns
   - Audit trail for masked data

2. **Encryption**
   - Optional log encryption
   - Key management
   - Secure key storage

3. **Access Control**
   - File permissions
   - User isolation
   - Network security

### Compliance

1. **GDPR Compliance**
   - Right to be forgotten
   - Data portability
   - Consent management

2. **Audit Requirements**
   - Comprehensive audit trails
   - Tamper detection
   - Retention policies

## 🚨 Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   # Fix log directory permissions
   sudo chown -R chatbot:chatbot /var/log/chatbot
   sudo chmod -R 755 /var/log/chatbot
   ```

2. **High Memory Usage**
   ```python
   # Reduce queue size
   config.queue_size = 5000
   ```

3. **Slow Logging**
   ```python
   # Increase performance threshold
   config.performance_alert_threshold_ms = 200.0
   ```

### Debug Mode

```python
# Enable debug logging
config.default_level = LogLevel.DEBUG
config.console_level = LogLevel.DEBUG
```

## 📈 Performance Tuning

### Optimization Tips

1. **Queue Size**: Adjust based on message volume
2. **Buffer Size**: Optimize for your I/O patterns
3. **Compression**: Enable for storage efficiency
4. **Async Operations**: Use for high-volume scenarios

### Benchmarking

```bash
# Run performance tests
python test_production_logging.py
```

## 🔄 Maintenance

### Regular Tasks

1. **Log Rotation**: Automatic, configurable
2. **Compression**: Automatic for old logs
3. **Cleanup**: Automatic based on retention policy
4. **Health Checks**: Continuous monitoring

### Backup Strategy

1. **Local Backups**: Compressed archives
2. **Remote Backups**: Cloud storage
3. **Verification**: Integrity checks
4. **Recovery**: Automated restoration

## 📚 Additional Resources

- [Logging Best Practices](https://docs.python.org/3/howto/logging.html)
- [Security Guidelines](https://owasp.org/www-project-logging-cheat-sheet/)
- [Performance Tuning](https://docs.python.org/3/howto/logging-cookbook.html)
- [Compliance Standards](https://www.gdpr.eu/)

---

**Note**: This logging system is designed for production use but should be thoroughly tested in your specific environment before deployment. 